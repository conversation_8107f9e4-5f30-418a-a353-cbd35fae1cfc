#!/usr/bin/env python3
"""
Local Testing Setup Script
Sets up the complete pipeline environment for local development and testing
without requiring GitHub Actions workflow
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('local_test_setup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class LocalTestSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.terraform_dir = self.project_root / "infrastructure" / "terraform"
        self.required_vars = [
            'TF_VAR_project_id',
            'TF_VAR_aws_private_key',
            'TF_VAR_aws_public_key',
            'TF_VAR_aws_hostname',
            'TF_VAR_github_token'
        ]
        
    def check_prerequisites(self):
        """Check if all required tools and configurations are available"""
        logger.info("Checking prerequisites...")
        
        # Check required tools
        tools = ['terraform', 'gcloud', 'mysql', 'ssh']
        missing_tools = []
        
        for tool in tools:
            try:
                subprocess.run([tool, '--version'], capture_output=True, check=True)
                logger.info(f"✓ {tool} is available")
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing_tools.append(tool)
                logger.error(f"✗ {tool} is not available")
        
        if missing_tools:
            logger.error(f"Missing required tools: {', '.join(missing_tools)}")
            return False
        
        # Check environment variables
        missing_vars = []
        for var in self.required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
                logger.error(f"✗ Environment variable {var} is not set")
            else:
                logger.info(f"✓ Environment variable {var} is set")
        
        if missing_vars:
            logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            logger.info("Please set these variables before running the test:")
            for var in missing_vars:
                logger.info(f"  export {var}='your_value_here'")
            return False
        
        # Check GCP authentication
        try:
            result = subprocess.run(['gcloud', 'auth', 'list'], capture_output=True, text=True)
            if 'ACTIVE' in result.stdout:
                logger.info("✓ GCP authentication is active")
            else:
                logger.error("✗ GCP authentication required. Run: gcloud auth login")
                return False
        except Exception as e:
            logger.error(f"✗ GCP authentication check failed: {e}")
            return False
        
        # Check terraform directory
        if not self.terraform_dir.exists():
            logger.error(f"✗ Terraform directory not found: {self.terraform_dir}")
            return False
        
        logger.info("✓ All prerequisites met")
        return True
    
    def setup_terraform_vars(self):
        """Create terraform.tfvars file from environment variables"""
        logger.info("Setting up Terraform variables...")
        
        tfvars_file = self.terraform_dir / "terraform.tfvars"
        
        # Read the example file to understand the structure
        example_file = self.terraform_dir / "terraform.tfvars.example"
        if not example_file.exists():
            logger.error(f"Example file not found: {example_file}")
            return False
        
        # Create tfvars content
        tfvars_content = f"""# Auto-generated terraform.tfvars for local testing
# Generated by local_test_setup.py

# GCP Configuration
project_id = "{os.getenv('TF_VAR_project_id')}"
region     = "europe-west10"
zone       = "europe-west10-c"

# VM Configuration
machine_type   = "e2-standard-4"
disk_size      = 10
environment    = "dev"
auto_delete_vm = true

# AWS Configuration
aws_hostname = "{os.getenv('TF_VAR_aws_hostname')}"
aws_user     = "{os.getenv('TF_VAR_aws_user', 'forge')}"

# AWS Private Key
aws_private_key = <<-EOT
{os.getenv('TF_VAR_aws_private_key')}
EOT

# AWS Public Key
aws_public_key = <<-EOT
{os.getenv('TF_VAR_aws_public_key')}
EOT

# GitHub Configuration
github_repo  = "https://github.com/travel-buddies/gcp_data_pipeline_tools_ingestion"
github_token = "{os.getenv('TF_VAR_github_token')}"

# Pipeline Configuration
pipeline_schedule = "0 2 * * 0"  # Weekly on Sunday at 2 AM
"""
        
        try:
            with open(tfvars_file, 'w') as f:
                f.write(tfvars_content)
            logger.info(f"✓ Terraform variables file created: {tfvars_file}")
            return True
        except Exception as e:
            logger.error(f"✗ Failed to create terraform.tfvars: {e}")
            return False
    
    def deploy_infrastructure(self):
        """Deploy the GCP infrastructure using Terraform"""
        logger.info("Deploying GCP infrastructure...")
        
        try:
            # Change to terraform directory
            os.chdir(self.terraform_dir)
            
            # Initialize Terraform
            logger.info("Initializing Terraform...")
            result = subprocess.run(['terraform', 'init'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Terraform init failed: {result.stderr}")
                return False
            
            # Plan the deployment
            logger.info("Planning Terraform deployment...")
            result = subprocess.run(['terraform', 'plan'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Terraform plan failed: {result.stderr}")
                return False
            
            # Apply the deployment
            logger.info("Applying Terraform deployment...")
            result = subprocess.run(['terraform', 'apply', '-auto-approve'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Terraform apply failed: {result.stderr}")
                return False
            
            logger.info("✓ Infrastructure deployed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Infrastructure deployment error: {e}")
            return False
        finally:
            # Return to project root
            os.chdir(self.project_root)
    
    def wait_for_vm_ready(self):
        """Wait for the VM to be ready and pipeline setup to complete"""
        logger.info("Waiting for VM to be ready...")
        
        import time
        max_wait_time = 600  # 10 minutes
        check_interval = 30  # 30 seconds
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            try:
                # Check if VM is running
                result = subprocess.run([
                    'gcloud', 'compute', 'instances', 'list',
                    '--filter', 'name:data-pipeline-dev-pipeline-vm',
                    '--format', 'value(status)'
                ], capture_output=True, text=True)
                
                if 'RUNNING' in result.stdout:
                    logger.info("✓ VM is running")
                    
                    # Wait a bit more for startup script to complete
                    logger.info("Waiting for startup script to complete...")
                    time.sleep(60)
                    return True
                
                logger.info(f"VM not ready yet, waiting... ({elapsed_time}/{max_wait_time}s)")
                time.sleep(check_interval)
                elapsed_time += check_interval
                
            except Exception as e:
                logger.warning(f"Error checking VM status: {e}")
                time.sleep(check_interval)
                elapsed_time += check_interval
        
        logger.error("VM did not become ready within the timeout period")
        return False
    
    def run_pipeline_test(self):
        """Run the database pipeline test"""
        logger.info("Running database pipeline test...")
        
        try:
            # Run the pipeline
            result = subprocess.run([
                sys.executable, 'run_pipeline.py'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            logger.info("Pipeline output:")
            logger.info(result.stdout)
            
            if result.stderr:
                logger.warning("Pipeline stderr:")
                logger.warning(result.stderr)
            
            if result.returncode == 0:
                logger.info("✓ Pipeline test completed successfully")
                return True
            else:
                logger.error(f"✗ Pipeline test failed with return code: {result.returncode}")
                return False
                
        except Exception as e:
            logger.error(f"Pipeline test error: {e}")
            return False
    
    def cleanup_infrastructure(self):
        """Clean up the deployed infrastructure"""
        logger.info("Cleaning up infrastructure...")
        
        try:
            os.chdir(self.terraform_dir)
            
            result = subprocess.run(['terraform', 'destroy', '-auto-approve'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✓ Infrastructure cleaned up successfully")
                return True
            else:
                logger.error(f"Infrastructure cleanup failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Infrastructure cleanup error: {e}")
            return False
        finally:
            os.chdir(self.project_root)
    
    def run_complete_test(self):
        """Run the complete local test cycle"""
        logger.info("=== Starting Complete Local Test Cycle ===")
        
        try:
            # Step 1: Check prerequisites
            if not self.check_prerequisites():
                logger.error("Prerequisites check failed")
                return False
            
            # Step 2: Setup Terraform variables
            if not self.setup_terraform_vars():
                logger.error("Terraform setup failed")
                return False
            
            # Step 3: Deploy infrastructure
            if not self.deploy_infrastructure():
                logger.error("Infrastructure deployment failed")
                return False
            
            # Step 4: Wait for VM to be ready
            if not self.wait_for_vm_ready():
                logger.error("VM readiness check failed")
                return False
            
            # Step 5: Run pipeline test
            if not self.run_pipeline_test():
                logger.error("Pipeline test failed")
                return False
            
            logger.info("=== Complete Local Test Cycle Successful ===")
            return True
            
        except Exception as e:
            logger.error(f"Complete test cycle error: {e}")
            return False
        
        finally:
            # Always attempt cleanup
            logger.info("Performing cleanup...")
            self.cleanup_infrastructure()

def main():
    """Main entry point"""
    setup = LocalTestSetup()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "check":
            success = setup.check_prerequisites()
        elif command == "deploy":
            success = setup.deploy_infrastructure()
        elif command == "test":
            success = setup.run_pipeline_test()
        elif command == "cleanup":
            success = setup.cleanup_infrastructure()
        elif command == "full":
            success = setup.run_complete_test()
        else:
            logger.error(f"Unknown command: {command}")
            logger.info("Available commands: check, deploy, test, cleanup, full")
            sys.exit(1)
    else:
        # Default: run complete test
        success = setup.run_complete_test()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
