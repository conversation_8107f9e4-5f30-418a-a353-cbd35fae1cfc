# GCP Data Pipeline Deployment Guide

## Issues Fixed ✅

### 1. Terraform Configuration Issues
- ✅ **Fixed terraform.tfvars**: Removed invalid `google_credentials` attribute
- ✅ **Fixed SSH Keys**: Proper multiline format using `<<-EOT` syntax
- ✅ **Fixed Backend**: Changed from GCS to local backend to avoid permission issues
- ✅ **Fixed Provider**: Added service account credentials file path

### 2. Authentication Issues
- ✅ **Service Account Authentication**: Created scripts for proper authentication
- ✅ **Environment Variables**: Set GOOGLE_APPLICATION_CREDENTIALS properly
- ✅ **API Access**: Enabled required GCP APIs

### 3. Permission Issues
- ✅ **Artifact Registry**: Commented out Cloud Run services due to permission restrictions
- ✅ **Service Account Creation**: Use existing service account instead of creating new one
- ✅ **API Enablement**: Manual API enablement due to Service Usage API restrictions

### 4. Terraform Destroy Issue
- ✅ **State Management**: Fixed Terraform state tracking
- ✅ **Resource Cleanup**: Proper destroy functionality working

## Current Working Configuration

### What's Deployed
- ✅ **VM Infrastructure**: GCP Compute Engine VM with proper networking
- ✅ **Networking**: VPC, subnet, firewall rules
- ✅ **Storage**: GCS bucket for pipeline data
- ✅ **Service Account**: Using existing VM service account

### What's Commented Out (Due to Permissions)
- ❌ **Cloud Run Services**: VM Manager, Database Pipeline, Orchestrator
- ❌ **Artifact Registry**: Container image repository
- ❌ **IAM Bindings**: Service account creation and role assignments

## How to Use

### 1. Deploy Infrastructure
```bash
# Authenticate and deploy
./scripts/deploy-with-auth.sh
```

### 2. Destroy Infrastructure
```bash
# Navigate to terraform directory
cd infrastructure/terraform

# Destroy all resources
terraform destroy -auto-approve
```

### 3. Check VM Status
```bash
# SSH into the VM
gcloud compute ssh data-pipeline-dev-pipeline-vm --zone=europe-west3-a --project=external-data-source-437915

# View startup logs
gcloud compute instances get-serial-port-output data-pipeline-dev-pipeline-vm --zone=europe-west3-a --project=external-data-source-437915
```

## Scripts Available

### Authentication & Deployment
- `scripts/setup-auth.sh` - Set up GCP authentication
- `scripts/deploy-with-auth.sh` - Complete deployment with authentication
- `scripts/enable-apis.sh` - Enable required GCP APIs

### Original Scripts (For Reference)
- `scripts/deploy-cloud-run.sh` - Original deployment script (needs permissions)

## File Structure

### Active Configuration
- `infrastructure/terraform/main.tf` - VM-only infrastructure
- `infrastructure/terraform/variables.tf` - Variable definitions
- `infrastructure/terraform/outputs.tf` - Output definitions
- `infrastructure/terraform/terraform.tfvars` - Configuration values

### Disabled Configuration (Due to Permissions)
- `infrastructure/terraform/main.tf.disabled` - Original main configuration
- `infrastructure/terraform/cloud-run.tf.disabled` - Cloud Run services

## Next Steps for Full Deployment

### To Enable Cloud Run Services:
1. **Grant Additional Permissions** to the service account:
   - `artifactregistry.repositories.create`
   - `artifactregistry.repositories.list`
   - `iam.serviceAccounts.create`
   - `iam.serviceAccounts.setIamPolicy`

2. **Enable Cloud Run Configuration**:
   ```bash
   cd infrastructure/terraform
   mv cloud-run.tf.disabled cloud-run.tf
   mv main.tf.disabled main-full.tf
   mv main.tf main-vm-only.tf
   mv main-full.tf main.tf
   ```

3. **Update Outputs**: Uncomment Cloud Run outputs in `outputs.tf`

4. **Deploy**: Run `./scripts/deploy-with-auth.sh`

## GitHub Token Issue

For the GitHub token permissions issue:
1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Edit your token to grant repository access
3. Select repository: `travel-buddies/gcp_data_pipeline_tools_ingestion`
4. Grant permissions: Contents (Read), Metadata (Read), Pull requests (Read)

## Troubleshooting

### Common Issues:
1. **"No objects need to be destroyed"**: This happens when Terraform state is out of sync. Use `terraform state list` to check current state.

2. **Permission Denied**: Ensure service account has proper roles and APIs are enabled.

3. **API Not Enabled**: Run `./scripts/enable-apis.sh` to enable required APIs.

### Useful Commands:
```bash
# Check Terraform state
terraform state list

# Check current authentication
gcloud auth list

# Check enabled APIs
gcloud services list --enabled

# Force refresh Terraform state
terraform refresh
```

## Summary

✅ **Working**: VM infrastructure deployment and destruction
✅ **Working**: Authentication with service account
✅ **Working**: Basic networking and storage setup
❌ **Pending**: Cloud Run services (requires additional permissions)
❌ **Pending**: Artifact Registry (requires additional permissions)

The basic infrastructure is now working correctly. You can deploy and destroy the VM infrastructure reliably. For the full Cloud Run orchestration, you'll need to request additional IAM permissions for the service account.
