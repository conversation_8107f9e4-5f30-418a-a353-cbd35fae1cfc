# Cloud Run Orchestrated Data Pipeline

A scalable, cloud-native data pipeline system that uses Cloud Run microservices to orchestrate VM lifecycle management, SSH connections to AWS EC2 instances, and database operations with comprehensive monitoring.

## 🏗️ Architecture

```
GitHub Actions → Cloud Run Orchestrator → VM Manager → GCP VM
                        ↓                      ↓           ↓
                 Database Pipeline ←→ SSH Connection → AWS EC2 (MariaDB)
                        ↓                      ↓           ↓
                   Query Results ←→ Data Processing → Auto Cleanup
```

### Key Components:
- **Cloud Run Orchestrator**: Coordinates the entire pipeline execution
- **VM Manager Service**: Handles VM creation, monitoring, and cleanup
- **Database Pipeline Service**: Manages SSH connections and database operations
- **AWS EC2**: Source MariaDB/MySQL database
- **GCP Compute Engine**: Temporary processing VM (auto-managed)
- **GitHub Actions**: Simplified trigger mechanism
- **Terraform**: Infrastructure as Code

## ✨ Cloud Run Benefits

✅ **Reduced GitHub Actions Load** - Only triggers orchestrator, all heavy operations in Cloud Run
✅ **Better Scalability** - Auto-scaling microservices architecture
✅ **Enhanced Monitoring** - Comprehensive logging and custom metrics
✅ **Improved Error Handling** - Retry logic and graceful degradation
✅ **Cost Optimization** - Pay-per-use pricing and automatic cleanup
✅ **Independent Scaling** - Each service scales based on demand

## 🚀 Quick Start

### Prerequisites
- Google Cloud Platform account
- AWS EC2 instance with MariaDB/MySQL
- GitHub repository
- SSH access to AWS EC2

### 1. Setup
```bash
git clone https://github.com/your-username/gcp_tools.git
cd gcp_tools
```

### 2. Configure GitHub Secrets
Set up the following secrets in your GitHub repository:
- `GCP_SA_KEY` - Google Cloud service account key
- `GCP_PROJECT_ID` - Your GCP project ID
- `AWS_PRIVATE_KEY` - SSH private key for AWS EC2
- `AWS_PUBLIC_KEY` - SSH public key for AWS EC2
- `AWS_HOSTNAME` - AWS EC2 IP address
- `AWS_USER` - AWS EC2 username

📖 **Detailed setup guide**: [docs/setup/github-secrets-setup.md](docs/setup/github-secrets-setup.md)

### 3. Deploy Cloud Run Services
```bash
# Deploy all Cloud Run services
chmod +x scripts/deploy-cloud-run-services.sh
./scripts/deploy-cloud-run-services.sh
```

Or via GitHub Actions:
1. Go to **Actions** → **Run Data Pipeline (Cloud Run Orchestrated)**
2. Select "deploy-cloud-services" action
3. Choose your environment (dev/staging/prod)

### 4. Run Pipeline

**Via GitHub Actions:**
1. Go to **Actions** → **Run Data Pipeline (Cloud Run Orchestrated)**
2. Select "run-cloud-orchestrated" action
3. Choose your environment (dev/staging/prod)
4. Monitor execution in the Actions tab

**Via Command Line:**
```bash
# Get orchestrator URL
ORCHESTRATOR_URL=$(gcloud run services describe data-pipeline-dev-orchestrator \
  --region=europe-west10 --format="value(status.url)")

# Trigger pipeline
curl -X POST "$ORCHESTRATOR_URL/execute-pipeline" \
  -H "Content-Type: application/json" \
  -d '{"pipeline_id":"manual-'$(date +%Y%m%d-%H%M%S)'"}'
```

**Via Local Testing:**
```bash
python local_test_setup.py full
```

The Cloud Run orchestrated pipeline will:
- Create a GCP VM with proper configuration (via VM Manager)
- Establish SSH connection to AWS EC2 (via Database Pipeline)
- Dump database schema and complete data
- Execute validation queries
- Clean up all resources automatically
- Provide comprehensive monitoring and error handling

### 5. Destroy Cloud Run Services (When Needed)
1. Go to **Actions** → **Run Data Pipeline (Cloud Run Orchestrated)**
2. Select "destroy-cloud-services" action
3. Choose your environment
4. All Cloud Run services will be deleted

## 📚 Documentation

- **[Cloud Run Architecture Guide](docs/CLOUD_RUN_ARCHITECTURE_GUIDE.md)** - Comprehensive guide to the new Cloud Run architecture
- **[Local Testing Guide](docs/LOCAL_TESTING_GUIDE.md)** - Step-by-step local testing instructions
- **[Workflow Guide](WORKFLOW_GUIDE.md)** - Complete workflow documentation

## 📁 Project Structure

```
gcp_data_pipeline_tools_ingestion/
├── infrastructure/           # Terraform infrastructure code
│   └── terraform/
│       ├── main.tf          # Main Terraform configuration
│       ├── cloud-run.tf     # Cloud Run services configuration
│       ├── variables.tf     # Variable definitions
│       ├── outputs.tf       # Output definitions
│       └── startup-script.sh # VM startup script
├── cloud-functions/         # Cloud Run microservices
│   ├── orchestrator/        # Pipeline orchestrator service
│   │   ├── main.py         # Orchestrator logic
│   │   ├── Dockerfile      # Container configuration
│   │   └── requirements.txt
│   ├── vm-manager/          # VM lifecycle management service
│   │   ├── main.py         # VM management logic
│   │   ├── Dockerfile      # Container configuration
│   │   └── requirements.txt
│   └── database-pipeline/   # Database operations service
│       ├── main.py         # Database pipeline logic
│       ├── Dockerfile      # Container configuration
│       └── requirements.txt
├── scripts/                 # Utility scripts
│   ├── deploy-cloud-run-services.sh # Cloud Run deployment
│   └── vm_pipeline_runner.py # VM-side pipeline execution
├── monitoring/              # Monitoring and alerting
│   └── pipeline-monitoring.py # Custom metrics and alerts
├── docs/                    # Documentation
│   ├── CLOUD_RUN_ARCHITECTURE_GUIDE.md # Architecture guide
│   └── LOCAL_TESTING_GUIDE.md # Testing guide
├── .github/workflows/       # GitHub Actions workflows
│   └── run-pipeline.yml    # Simplified Cloud Run workflow
├── local_test_setup.py      # Local testing framework
├── run_pipeline.py          # Legacy pipeline script
└── requirements.txt         # Python dependencies
```

## 🔄 Pipeline Process

1. **VM Creation**: Terraform creates a GCP Compute Engine VM
2. **SSH Setup**: VM configures SSH keys and tests AWS EC2 connection
3. **Data Extraction**: Connects to AWS EC2 and dumps MariaDB database
4. **Data Processing**: Imports data locally and performs transformations
5. **Upload**: Processed data uploaded to Google Cloud Storage
6. **Cleanup**: VM self-destructs after completion

## 📊 Monitoring

### View Logs
```bash
# VM startup logs
gcloud compute instances get-serial-port-output VM_NAME --zone=ZONE

# Cloud Logging
gcloud logging read "resource.type=gce_instance" --limit=50
```

### SSH to VM (for debugging)
```bash
gcloud compute ssh VM_NAME --zone=ZONE
```

## 🔧 Configuration

### Environment Variables
Configure in `config/environments/{env}/terraform.tfvars.template`:

```hcl
project_id = "your-gcp-project"
aws_hostname = "***********"
aws_user = "forge"
machine_type = "e2-standard-4"
```

### Pipeline Schedule
- **Development**: Manual trigger
- **Staging**: Daily (for testing)
- **Production**: Weekly (Sunday 2 AM UTC)

## 🧪 Testing

### Local Testing
```bash
# Install dependencies
pip install -r requirements.txt

# Run tests
pytest tests/

# Validate Terraform
cd infrastructure/terraform
terraform validate
terraform plan
```

### Integration Testing
The pipeline includes automated tests for:
- SSH connectivity
- Database operations
- GCS uploads
- Infrastructure deployment

## 🔒 Security Features

- **Ephemeral VMs**: Temporary infrastructure reduces attack surface
- **SSH Key Management**: Secure key handling and rotation
- **IAM Permissions**: Least-privilege access controls
- **Encrypted Storage**: Data encrypted in transit and at rest
- **Audit Logging**: Complete audit trail in Cloud Logging

## 📈 Scaling

### Performance Tuning
- Adjust VM machine type based on data volume
- Configure parallel processing for large datasets
- Optimize database dump and import operations

### Multi-Environment Support
- Separate configurations for dev/staging/prod
- Environment-specific resource sizing
- Isolated state management

## 🚨 Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Check AWS security groups
   - Verify SSH key format
   - Ensure EC2 instance is running

2. **Terraform Errors**
   - Verify GCP permissions
   - Check resource quotas
   - Validate configuration syntax

3. **Pipeline Failures**
   - Review VM startup logs
   - Check database connectivity
   - Verify GCS permissions

📖 **Full troubleshooting guide**: [docs/setup/README.md](docs/setup/README.md)

---
