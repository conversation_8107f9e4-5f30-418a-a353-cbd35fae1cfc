"""
VM Manager Cloud Run Service
Handles VM lifecycle operations:
- VM Creation with startup script
- VM Configuration and monitoring
- VM Cleanup and resource deletion
"""

import os
import json
import logging
import time
import subprocess
from datetime import datetime
from typing import Dict, Any, Optional
from google.cloud import compute_v1
from google.cloud import logging as cloud_logging
from flask import Flask, request, jsonify

# Setup logging
cloud_logging.Client().setup_logging()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class VMManager:
    def __init__(self):
        self.compute_client = compute_v1.InstancesClient()
        self.zones_client = compute_v1.ZonesClient()
        self.networks_client = compute_v1.NetworksClient()
        self.subnetworks_client = compute_v1.SubnetworksClient()
        self.firewalls_client = compute_v1.FirewallsClient()
        
        logger.info("VM Manager initialized")

    def create_vm_with_terraform(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create VM using Terraform configuration
        """
        try:
            pipeline_id = config.get('pipeline_id')
            project_id = config.get('project_id')
            zone = config.get('zone')
            environment = config.get('environment', 'dev')
            
            logger.info(f"Creating VM for pipeline {pipeline_id}")
            
            # Create terraform.tfvars content
            tfvars_content = self._generate_tfvars(config)
            
            # Write tfvars to temporary file
            tfvars_path = f"/tmp/terraform-{pipeline_id}.tfvars"
            with open(tfvars_path, 'w') as f:
                f.write(tfvars_content)
            
            # Set up Terraform working directory
            terraform_dir = "/tmp/terraform-workspace"
            os.makedirs(terraform_dir, exist_ok=True)
            
            # Copy Terraform files (this would be done via volume mount in real deployment)
            self._setup_terraform_files(terraform_dir)
            
            # Initialize Terraform
            init_result = subprocess.run([
                'terraform', 'init',
                f'-backend-config=bucket={project_id}-terraform-state-{environment}',
                f'-backend-config=prefix=terraform/state/{environment}'
            ], cwd=terraform_dir, capture_output=True, text=True)
            
            if init_result.returncode != 0:
                raise Exception(f"Terraform init failed: {init_result.stderr}")
            
            # Apply Terraform
            apply_result = subprocess.run([
                'terraform', 'apply', '-auto-approve', f'-var-file={tfvars_path}'
            ], cwd=terraform_dir, capture_output=True, text=True)
            
            if apply_result.returncode != 0:
                raise Exception(f"Terraform apply failed: {apply_result.stderr}")
            
            # Get VM information from Terraform output
            vm_name_result = subprocess.run([
                'terraform', 'output', '-raw', 'vm_name'
            ], cwd=terraform_dir, capture_output=True, text=True)
            
            vm_zone_result = subprocess.run([
                'terraform', 'output', '-raw', 'vm_zone'
            ], cwd=terraform_dir, capture_output=True, text=True)
            
            vm_name = vm_name_result.stdout.strip() if vm_name_result.returncode == 0 else f"data-pipeline-{environment}-pipeline-vm"
            vm_zone = vm_zone_result.stdout.strip() if vm_zone_result.returncode == 0 else zone
            
            logger.info(f"VM created successfully: {vm_name} in zone {vm_zone}")
            
            return {
                'success': True,
                'vm_name': vm_name,
                'vm_zone': vm_zone,
                'project_id': project_id,
                'terraform_dir': terraform_dir,
                'message': 'VM created successfully'
            }
            
        except Exception as e:
            error_msg = f"VM creation failed: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def _generate_tfvars(self, config: Dict[str, Any]) -> str:
        """Generate terraform.tfvars content"""
        environment = config.get('environment', 'dev')
        disk_size = 16 if environment == 'prod' else 10
        
        tfvars = f'''# Auto-generated terraform.tfvars for pipeline
project_id = "{config.get('project_id')}"
project_name = "data-pipeline"
region = "europe-west10"
zone = "{config.get('zone')}"
machine_type = "{config.get('machine_type', 'e2-standard-4')}"
disk_size = {disk_size}
aws_private_key = """{config.get('aws_private_key')}"""
aws_public_key = """{config.get('aws_public_key')}"""
aws_hostname = "{config.get('aws_hostname')}"
aws_user = "{config.get('aws_user', 'forge')}"
github_repo = "{config.get('github_repo')}"
github_token = "{config.get('github_token')}"
environment = "{environment}"
auto_delete_vm = true
'''
        return tfvars

    def _setup_terraform_files(self, terraform_dir: str):
        """
        Setup Terraform files in working directory
        In production, this would be handled by mounting the terraform directory
        """
        # This is a placeholder - in real deployment, terraform files would be
        # available via volume mount or copied during container build
        logger.info(f"Setting up Terraform files in {terraform_dir}")
        
        # For now, we'll assume terraform files are available in the container
        # at /app/terraform/ and copy them to the working directory
        import shutil
        try:
            if os.path.exists('/app/terraform'):
                shutil.copytree('/app/terraform', terraform_dir, dirs_exist_ok=True)
            else:
                logger.warning("Terraform files not found in container")
        except Exception as e:
            logger.warning(f"Could not copy terraform files: {e}")

    def wait_for_vm_startup(self, vm_name: str, zone: str, project_id: str, timeout_minutes: int = 15) -> Dict[str, Any]:
        """
        Wait for VM startup script to complete
        """
        try:
            logger.info(f"Waiting for VM startup: {vm_name}")
            
            timeout_seconds = timeout_minutes * 60
            check_interval = 10
            elapsed_time = 0
            
            while elapsed_time < timeout_seconds:
                try:
                    # Get serial port output to check startup progress
                    result = subprocess.run([
                        'gcloud', 'compute', 'instances', 'get-serial-port-output',
                        vm_name, f'--zone={zone}', f'--project={project_id}'
                    ], capture_output=True, text=True, timeout=30)
                    
                    if result.returncode == 0:
                        serial_output = result.stdout
                        
                        # Check for completion markers
                        if any(marker in serial_output for marker in [
                            "Data Pipeline VM Startup Script Completed Successfully",
                            "STARTUP_COMPLETE:",
                            "=== Data Pipeline VM Startup Script Completed Successfully"
                        ]):
                            logger.info("VM startup completed successfully")
                            return {
                                'success': True,
                                'message': 'VM startup completed',
                                'startup_logs': serial_output[-2000:]  # Last 2000 characters
                            }
                    
                    logger.info(f"VM startup in progress... ({elapsed_time}/{timeout_seconds}s)")
                    time.sleep(check_interval)
                    elapsed_time += check_interval
                    
                except subprocess.TimeoutExpired:
                    logger.warning("Timeout getting serial output, continuing...")
                    time.sleep(check_interval)
                    elapsed_time += check_interval
            
            # Timeout reached
            logger.error(f"VM startup timeout after {timeout_minutes} minutes")
            return {
                'success': False,
                'error': f'VM startup timeout after {timeout_minutes} minutes'
            }
            
        except Exception as e:
            error_msg = f"VM startup wait error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def cleanup_vm(self, vm_name: str, zone: str, project_id: str, terraform_dir: str = None) -> Dict[str, Any]:
        """
        Cleanup VM and associated resources using Terraform
        """
        try:
            logger.info(f"Cleaning up VM: {vm_name}")
            
            if not terraform_dir:
                terraform_dir = "/tmp/terraform-workspace"
            
            if not os.path.exists(terraform_dir):
                logger.warning("Terraform directory not found, attempting direct VM deletion")
                return self._direct_vm_cleanup(vm_name, zone, project_id)
            
            # Use Terraform to destroy resources
            destroy_result = subprocess.run([
                'terraform', 'destroy', '-auto-approve'
            ], cwd=terraform_dir, capture_output=True, text=True)
            
            if destroy_result.returncode == 0:
                logger.info("Resources cleaned up successfully via Terraform")
                
                # Clean up terraform directory
                import shutil
                try:
                    shutil.rmtree(terraform_dir)
                except Exception as e:
                    logger.warning(f"Could not clean up terraform directory: {e}")
                
                return {
                    'success': True,
                    'message': 'Resources cleaned up successfully'
                }
            else:
                logger.error(f"Terraform destroy failed: {destroy_result.stderr}")
                # Fallback to direct cleanup
                return self._direct_vm_cleanup(vm_name, zone, project_id)
                
        except Exception as e:
            error_msg = f"VM cleanup error: {str(e)}"
            logger.error(error_msg)
            # Fallback to direct cleanup
            return self._direct_vm_cleanup(vm_name, zone, project_id)

    def _direct_vm_cleanup(self, vm_name: str, zone: str, project_id: str) -> Dict[str, Any]:
        """
        Direct VM cleanup using gcloud commands as fallback
        """
        try:
            logger.info(f"Attempting direct VM cleanup: {vm_name}")
            
            # Delete VM instance
            delete_result = subprocess.run([
                'gcloud', 'compute', 'instances', 'delete', vm_name,
                f'--zone={zone}', f'--project={project_id}', '--quiet'
            ], capture_output=True, text=True)
            
            if delete_result.returncode == 0:
                logger.info("VM deleted successfully")
                return {
                    'success': True,
                    'message': 'VM deleted successfully (direct cleanup)'
                }
            else:
                logger.error(f"VM deletion failed: {delete_result.stderr}")
                return {
                    'success': False,
                    'error': f'VM deletion failed: {delete_result.stderr}'
                }
                
        except Exception as e:
            error_msg = f"Direct VM cleanup error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

# Flask routes
vm_manager = VMManager()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'vm-manager'})

@app.route('/create-vm', methods=['POST'])
def create_vm():
    """Create VM with configuration"""
    try:
        config = request.get_json()
        if not config:
            return jsonify({'success': False, 'error': 'No configuration provided'}), 400
        
        result = vm_manager.create_vm_with_terraform(config)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"Create VM error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/wait-startup', methods=['POST'])
def wait_startup():
    """Wait for VM startup completion"""
    try:
        data = request.get_json()
        vm_name = data.get('vm_name')
        zone = data.get('zone')
        project_id = data.get('project_id')
        
        if not all([vm_name, zone, project_id]):
            return jsonify({
                'success': False,
                'error': 'Missing required parameters: vm_name, zone, project_id'
            }), 400
        
        result = vm_manager.wait_for_vm_startup(vm_name, zone, project_id)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"Wait startup error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/cleanup-vm', methods=['POST'])
def cleanup_vm():
    """Cleanup VM and resources"""
    try:
        data = request.get_json()
        vm_name = data.get('vm_name')
        zone = data.get('zone')
        project_id = data.get('project_id')
        terraform_dir = data.get('terraform_dir')
        
        if not all([vm_name, zone, project_id]):
            return jsonify({
                'success': False,
                'error': 'Missing required parameters: vm_name, zone, project_id'
            }), 400
        
        result = vm_manager.cleanup_vm(vm_name, zone, project_id, terraform_dir)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Cleanup VM error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
