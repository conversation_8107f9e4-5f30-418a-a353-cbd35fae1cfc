"""
Cloud Run Data Pipeline Orchestrator
Main orchestrator that manages the complete data pipeline lifecycle:
1. VM Creation and Configuration
2. SSH Connection Testing
3. Database Dump Operations
4. Query Execution
5. Resource Cleanup
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from google.cloud import compute_v1
from google.cloud import logging as cloud_logging
from google.cloud import storage
from google.auth.transport.requests import Request
from google.oauth2 import service_account
import google.auth
import requests
from flask import Flask, request, jsonify

# Setup logging
cloud_logging.Client().setup_logging()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class PipelineOrchestrator:
    def __init__(self):
        self.project_id = os.environ.get('GCP_PROJECT_ID')
        self.region = os.environ.get('GCP_REGION', 'europe-west10')
        self.zone = os.environ.get('GCP_ZONE', 'europe-west10-c')
        
        # Cloud Run service URLs
        self.vm_manager_url = os.environ.get('VM_MANAGER_URL')
        self.db_pipeline_url = os.environ.get('DB_PIPELINE_URL')
        
        # Pipeline configuration
        self.pipeline_config = {
            'aws_hostname': os.environ.get('AWS_HOSTNAME'),
            'aws_user': os.environ.get('AWS_USER', 'forge'),
            'aws_private_key': os.environ.get('AWS_PRIVATE_KEY'),
            'aws_public_key': os.environ.get('AWS_PUBLIC_KEY'),
            'github_repo': os.environ.get('GITHUB_REPO'),
            'github_token': os.environ.get('GITHUB_TOKEN'),
            'machine_type': os.environ.get('VM_MACHINE_TYPE', 'e2-standard-4'),
            'disk_size': int(os.environ.get('VM_DISK_SIZE', '10')),
            'environment': os.environ.get('ENVIRONMENT', 'dev')
        }
        
        # Initialize GCP clients
        self.compute_client = compute_v1.InstancesClient()
        self.storage_client = storage.Client()
        
        logger.info("Pipeline Orchestrator initialized")

    def _get_authenticated_session(self) -> requests.Session:
        """Get an authenticated requests session for service-to-service calls"""
        try:
            # Get default credentials
            credentials, project = google.auth.default()

            # Create a requests session
            session = requests.Session()

            # Add authentication to the session
            auth_req = Request()
            credentials.refresh(auth_req)

            # Add the authorization header
            session.headers.update({
                'Authorization': f'Bearer {credentials.token}',
                'Content-Type': 'application/json'
            })

            return session
        except Exception as e:
            logger.error(f"Failed to create authenticated session: {e}")
            # Fallback to regular session for local testing
            session = requests.Session()
            session.headers.update({'Content-Type': 'application/json'})
            return session

    async def execute_pipeline(self, pipeline_id: str) -> Dict[str, Any]:
        """
        Execute the complete data pipeline process
        """
        logger.info(f"Starting pipeline execution: {pipeline_id}")
        
        pipeline_status = {
            'pipeline_id': pipeline_id,
            'status': 'running',
            'start_time': datetime.utcnow().isoformat(),
            'steps': {},
            'vm_name': None,
            'errors': []
        }
        
        try:
            # Step 1: Create and configure VM
            logger.info("Step 1: Creating and configuring VM")
            vm_result = await self._create_vm(pipeline_id)
            pipeline_status['steps']['vm_creation'] = vm_result
            pipeline_status['vm_name'] = vm_result.get('vm_name')
            
            if not vm_result.get('success'):
                raise Exception(f"VM creation failed: {vm_result.get('error')}")
            
            # Step 2: Wait for VM to be ready
            logger.info("Step 2: Waiting for VM startup completion")
            startup_result = await self._wait_for_vm_startup(pipeline_status['vm_name'])
            pipeline_status['steps']['vm_startup'] = startup_result
            
            if not startup_result.get('success'):
                raise Exception(f"VM startup failed: {startup_result.get('error')}")
            
            # Step 3: Test SSH connection
            logger.info("Step 3: Testing SSH connection to AWS EC2")
            ssh_result = await self._test_ssh_connection(pipeline_status['vm_name'])
            pipeline_status['steps']['ssh_test'] = ssh_result
            
            if not ssh_result.get('success'):
                raise Exception(f"SSH connection failed: {ssh_result.get('error')}")
            
            # Step 4: Execute database operations
            logger.info("Step 4: Executing database dump and operations")
            db_result = await self._execute_database_operations(pipeline_status['vm_name'])
            pipeline_status['steps']['database_operations'] = db_result
            
            if not db_result.get('success'):
                raise Exception(f"Database operations failed: {db_result.get('error')}")
            
            # Step 5: Execute validation query
            logger.info("Step 5: Executing validation query")
            query_result = await self._execute_validation_query(pipeline_status['vm_name'])
            pipeline_status['steps']['validation_query'] = query_result
            
            if not query_result.get('success'):
                raise Exception(f"Validation query failed: {query_result.get('error')}")
            
            # Success
            pipeline_status['status'] = 'completed'
            pipeline_status['end_time'] = datetime.utcnow().isoformat()
            pipeline_status['result'] = query_result.get('result')
            
            logger.info(f"Pipeline {pipeline_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Pipeline {pipeline_id} failed: {str(e)}")
            pipeline_status['status'] = 'failed'
            pipeline_status['end_time'] = datetime.utcnow().isoformat()
            pipeline_status['errors'].append(str(e))
            
        finally:
            # Step 6: Cleanup resources
            logger.info("Step 6: Cleaning up resources")
            cleanup_result = await self._cleanup_resources(pipeline_status['vm_name'])
            pipeline_status['steps']['cleanup'] = cleanup_result
        
        return pipeline_status

    async def _create_vm(self, pipeline_id: str) -> Dict[str, Any]:
        """Create VM using VM Manager Cloud Run service"""
        try:
            vm_config = {
                'pipeline_id': pipeline_id,
                'project_id': self.project_id,
                'zone': self.zone,
                'machine_type': self.pipeline_config['machine_type'],
                'disk_size': self.pipeline_config['disk_size'],
                'environment': self.pipeline_config['environment'],
                'aws_hostname': self.pipeline_config['aws_hostname'],
                'aws_user': self.pipeline_config['aws_user'],
                'aws_private_key': self.pipeline_config['aws_private_key'],
                'aws_public_key': self.pipeline_config['aws_public_key'],
                'github_repo': self.pipeline_config['github_repo'],
                'github_token': self.pipeline_config['github_token']
            }

            session = self._get_authenticated_session()
            response = session.post(
                f"{self.vm_manager_url}/create-vm",
                json=vm_config,
                timeout=300
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"VM created successfully: {result.get('vm_name')}")
                return result
            else:
                error_msg = f"VM creation failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"VM creation error: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    async def _wait_for_vm_startup(self, vm_name: str) -> Dict[str, Any]:
        """Wait for VM startup script to complete"""
        try:
            session = self._get_authenticated_session()
            response = session.post(
                f"{self.vm_manager_url}/wait-startup",
                json={'vm_name': vm_name, 'zone': self.zone, 'project_id': self.project_id},
                timeout=900  # 15 minutes
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("VM startup completed successfully")
                return result
            else:
                error_msg = f"VM startup wait failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"VM startup wait error: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    async def _test_ssh_connection(self, vm_name: str) -> Dict[str, Any]:
        """Test SSH connection to AWS EC2"""
        try:
            session = self._get_authenticated_session()
            response = session.post(
                f"{self.db_pipeline_url}/test-ssh",
                json={'vm_name': vm_name, 'zone': self.zone, 'project_id': self.project_id},
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("SSH connection test completed")
                return result
            else:
                error_msg = f"SSH test failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"SSH test error: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    async def _execute_database_operations(self, vm_name: str) -> Dict[str, Any]:
        """Execute database dump operations"""
        try:
            session = self._get_authenticated_session()
            response = session.post(
                f"{self.db_pipeline_url}/dump-database",
                json={'vm_name': vm_name, 'zone': self.zone, 'project_id': self.project_id},
                timeout=1800  # 30 minutes
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("Database dump completed")
                return result
            else:
                error_msg = f"Database dump failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Database dump error: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    async def _execute_validation_query(self, vm_name: str) -> Dict[str, Any]:
        """Execute validation query"""
        try:
            session = self._get_authenticated_session()
            response = session.post(
                f"{self.db_pipeline_url}/execute-query",
                json={'vm_name': vm_name, 'zone': self.zone, 'project_id': self.project_id},
                timeout=300
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Validation query completed: {result.get('result')}")
                return result
            else:
                error_msg = f"Validation query failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Validation query error: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    async def _cleanup_resources(self, vm_name: str) -> Dict[str, Any]:
        """Cleanup all resources"""
        try:
            if not vm_name:
                return {'success': True, 'message': 'No VM to cleanup'}

            session = self._get_authenticated_session()
            response = session.post(
                f"{self.vm_manager_url}/cleanup-vm",
                json={'vm_name': vm_name, 'zone': self.zone, 'project_id': self.project_id},
                timeout=300
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("Resource cleanup completed")
                return result
            else:
                error_msg = f"Cleanup failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Cleanup error: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

# Flask routes
orchestrator = PipelineOrchestrator()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'pipeline-orchestrator'})

@app.route('/execute-pipeline', methods=['POST'])
def execute_pipeline():
    """Execute the complete data pipeline"""
    try:
        data = request.get_json() or {}
        pipeline_id = data.get('pipeline_id', f"pipeline-{datetime.utcnow().strftime('%Y%m%d-%H%M%S')}")
        
        logger.info(f"Received pipeline execution request: {pipeline_id}")
        
        # Execute pipeline asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(orchestrator.execute_pipeline(pipeline_id))
        loop.close()
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Pipeline execution error: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

@app.route('/pipeline-status/<pipeline_id>', methods=['GET'])
def get_pipeline_status(pipeline_id):
    """Get pipeline status (placeholder for future implementation)"""
    return jsonify({
        'pipeline_id': pipeline_id,
        'status': 'not_implemented',
        'message': 'Status tracking not yet implemented'
    })

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
