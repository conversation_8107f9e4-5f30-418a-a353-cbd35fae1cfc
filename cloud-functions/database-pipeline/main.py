"""
Database Pipeline Cloud Run Service
Handles database operations via SSH:
- SSH connection testing
- Database dump operations
- Query execution
- Data validation
"""

import os
import json
import logging
import subprocess
import tempfile
import time
from datetime import datetime
from typing import Dict, Any, Optional
from google.cloud import logging as cloud_logging
from google.cloud import storage
from flask import Flask, request, jsonify

# Setup logging
cloud_logging.Client().setup_logging()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

def retry_on_failure(max_retries=3, delay=5):
    """Decorator to retry functions on failure"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.error(f"Function {func.__name__} failed after {max_retries} attempts: {e}")
                        raise
                    else:
                        logger.warning(f"Function {func.__name__} failed on attempt {attempt + 1}, retrying in {delay}s: {e}")
                        time.sleep(delay)
            return None
        return wrapper
    return decorator

class DatabasePipeline:
    def __init__(self):
        self.storage_client = storage.Client()
        logger.info("Database Pipeline initialized")

    @retry_on_failure(max_retries=3, delay=10)
    def test_ssh_connection(self, vm_name: str, zone: str, project_id: str) -> Dict[str, Any]:
        """
        Test SSH connection to AWS EC2 via the GCP VM
        """
        try:
            logger.info(f"Testing SSH connection via VM: {vm_name}")
            
            # Create test script
            test_script = '''#!/bin/bash
set -e

echo "=== SSH Connection Test Started ==="

# Load SSH agent environment
if [ -f ~/.ssh/load_ssh_agent.sh ]; then
    source ~/.ssh/load_ssh_agent.sh
else
    echo "SSH agent loader not found"
    eval "$(ssh-agent -s)"
    ssh-add ~/.ssh/aws_private_key
fi

# Test SSH connection
echo "Testing SSH connection to trips..."
if ssh -o ConnectTimeout=30 -o BatchMode=yes trips "echo 'SSH connection successful'; hostname; date"; then
    echo "✓ SSH connection test passed"
    
    # Test MySQL availability
    echo "Testing MySQL availability..."
    if ssh trips "mysql --version" 2>/dev/null; then
        echo "✓ MySQL is available on remote host"
        
        # Test database connectivity
        echo "Testing database connectivity..."
        if ssh trips "mysql -e 'SHOW DATABASES;'" 2>/dev/null; then
            echo "✓ Database connection test passed"
        else
            echo "⚠ Database connection test failed, but SSH works"
        fi
    else
        echo "⚠ MySQL not available, but SSH works"
    fi
    
    echo "=== SSH Connection Test Completed Successfully ==="
    exit 0
else
    echo "✗ SSH connection test failed"
    echo "=== SSH Connection Test Failed ==="
    exit 1
fi
'''
            
            # Execute test on VM
            result = self._execute_script_on_vm(vm_name, zone, project_id, test_script, "ssh_test")
            
            if result.get('success'):
                logger.info("SSH connection test passed")
                return {
                    'success': True,
                    'message': 'SSH connection test passed',
                    'output': result.get('output', '')
                }
            else:
                logger.error("SSH connection test failed")
                return {
                    'success': False,
                    'error': 'SSH connection test failed',
                    'output': result.get('output', '')
                }
                
        except Exception as e:
            error_msg = f"SSH test error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def dump_database(self, vm_name: str, zone: str, project_id: str) -> Dict[str, Any]:
        """
        Dump database from AWS EC2 via SSH
        """
        try:
            logger.info(f"Starting database dump via VM: {vm_name}")
            
            # Create dump script
            dump_script = '''#!/bin/bash
set -e

echo "=== Database Dump Started ==="

# Load SSH agent environment
if [ -f ~/.ssh/load_ssh_agent.sh ]; then
    source ~/.ssh/load_ssh_agent.sh
else
    echo "Setting up SSH agent..."
    eval "$(ssh-agent -s)"
    ssh-add ~/.ssh/aws_private_key
fi

# Create target directory
mkdir -p /home/<USER>/dumps
cd /home/<USER>/dumps

echo "Dumping forge database schema..."
if ssh trips "mysqldump --no-data forge --ignore-table=forge.activity_log" > forge_schema.sql; then
    echo "✓ Schema dump completed"
    echo "Schema file size: $(wc -l < forge_schema.sql) lines"
else
    echo "✗ Schema dump failed"
    exit 1
fi

echo "Dumping forge database data..."
if ssh trips "mysqldump --single-transaction forge --ignore-table=forge.activity_log" > forge_complete.sql; then
    echo "✓ Complete database dump completed"
    echo "Complete dump file size: $(wc -l < forge_complete.sql) lines"
    
    # Show first few lines for verification
    echo "=== First 20 lines of dump ==="
    head -n 20 forge_complete.sql
    echo "=== End of sample ==="
    
    # Upload to Google Cloud Storage
    echo "Uploading dump to Cloud Storage..."
    BUCKET_NAME="${project_id}-pipeline-data-$(date +%Y%m%d)"
    DUMP_FILE="forge_dump_$(date +%Y%m%d_%H%M%S).sql"
    
    if gsutil cp forge_complete.sql gs://$BUCKET_NAME/$DUMP_FILE 2>/dev/null || \
       gcloud storage cp forge_complete.sql gs://$BUCKET_NAME/$DUMP_FILE; then
        echo "✓ Dump uploaded to Cloud Storage: gs://$BUCKET_NAME/$DUMP_FILE"
    else
        echo "⚠ Could not upload to Cloud Storage, but dump completed locally"
    fi
    
else
    echo "✗ Complete database dump failed"
    exit 1
fi

echo "=== Database Dump Completed Successfully ==="
'''
            
            # Execute dump on VM
            result = self._execute_script_on_vm(vm_name, zone, project_id, dump_script, "database_dump")
            
            if result.get('success'):
                logger.info("Database dump completed successfully")
                return {
                    'success': True,
                    'message': 'Database dump completed',
                    'output': result.get('output', '')
                }
            else:
                logger.error("Database dump failed")
                return {
                    'success': False,
                    'error': 'Database dump failed',
                    'output': result.get('output', '')
                }
                
        except Exception as e:
            error_msg = f"Database dump error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def execute_query(self, vm_name: str, zone: str, project_id: str) -> Dict[str, Any]:
        """
        Execute validation query on the database
        """
        try:
            logger.info(f"Executing validation query via VM: {vm_name}")
            
            # Create query script
            query_script = '''#!/bin/bash
set -e

echo "=== Database Query Execution Started ==="

# Load SSH agent environment
if [ -f ~/.ssh/load_ssh_agent.sh ]; then
    source ~/.ssh/load_ssh_agent.sh
else
    echo "Setting up SSH agent..."
    eval "$(ssh-agent -s)"
    ssh-add ~/.ssh/aws_private_key
fi

echo "Executing validation query..."
QUERY="SELECT COUNT(*) FROM trips WHERE is_booker_version=1"

echo "Query: $QUERY"

if RESULT=$(ssh trips "mysql -e \\"$QUERY\\" forge 2>/dev/null | tail -n 1"); then
    echo "✓ Query executed successfully"
    echo "DB Query output resulting in these rows: $RESULT"
    
    # Also save result to file for later retrieval
    echo "$RESULT" > /home/<USER>/query_result.txt
    echo "Result saved to /home/<USER>/query_result.txt"
    
    echo "=== Database Query Execution Completed Successfully ==="
    echo "FINAL_RESULT:$RESULT"
else
    echo "✗ Query execution failed"
    echo "=== Database Query Execution Failed ==="
    exit 1
fi
'''
            
            # Execute query on VM
            result = self._execute_script_on_vm(vm_name, zone, project_id, query_script, "query_execution")
            
            if result.get('success'):
                # Extract result from output
                output = result.get('output', '')
                query_result = None
                
                # Look for the result in the output
                for line in output.split('\n'):
                    if 'DB Query output resulting in these rows:' in line:
                        try:
                            query_result = line.split(':')[-1].strip()
                            break
                        except:
                            pass
                    elif line.startswith('FINAL_RESULT:'):
                        try:
                            query_result = line.split(':', 1)[1].strip()
                            break
                        except:
                            pass
                
                logger.info(f"Query executed successfully, result: {query_result}")
                return {
                    'success': True,
                    'message': 'Query executed successfully',
                    'result': query_result,
                    'output': output
                }
            else:
                logger.error("Query execution failed")
                return {
                    'success': False,
                    'error': 'Query execution failed',
                    'output': result.get('output', '')
                }
                
        except Exception as e:
            error_msg = f"Query execution error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def _execute_script_on_vm(self, vm_name: str, zone: str, project_id: str, script_content: str, script_name: str) -> Dict[str, Any]:
        """
        Execute a script on the VM via gcloud compute ssh
        """
        try:
            # Create temporary script file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
                f.write(script_content)
                local_script_path = f.name
            
            remote_script_path = f"/tmp/{script_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sh"
            
            # Copy script to VM
            copy_result = subprocess.run([
                'gcloud', 'compute', 'scp', local_script_path,
                f'{vm_name}:{remote_script_path}',
                f'--zone={zone}',
                f'--project={project_id}'
            ], capture_output=True, text=True, timeout=60)
            
            if copy_result.returncode != 0:
                raise Exception(f"Failed to copy script to VM: {copy_result.stderr}")
            
            # Execute script on VM
            exec_result = subprocess.run([
                'gcloud', 'compute', 'ssh', vm_name,
                f'--zone={zone}',
                f'--project={project_id}',
                '--command', f'sudo -u pipeline bash {remote_script_path}'
            ], capture_output=True, text=True, timeout=1800)  # 30 minutes timeout
            
            # Clean up local script file
            try:
                os.unlink(local_script_path)
            except:
                pass
            
            # Clean up remote script file
            subprocess.run([
                'gcloud', 'compute', 'ssh', vm_name,
                f'--zone={zone}',
                f'--project={project_id}',
                '--command', f'rm -f {remote_script_path}'
            ], capture_output=True, text=True, timeout=30)
            
            if exec_result.returncode == 0:
                return {
                    'success': True,
                    'output': exec_result.stdout,
                    'stderr': exec_result.stderr
                }
            else:
                return {
                    'success': False,
                    'output': exec_result.stdout,
                    'stderr': exec_result.stderr,
                    'error': f'Script execution failed with return code {exec_result.returncode}'
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Script execution timeout'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Script execution error: {str(e)}'
            }

# Flask routes
db_pipeline = DatabasePipeline()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'database-pipeline'})

@app.route('/test-ssh', methods=['POST'])
def test_ssh():
    """Test SSH connection"""
    try:
        data = request.get_json()
        vm_name = data.get('vm_name')
        zone = data.get('zone')
        project_id = data.get('project_id')
        
        if not all([vm_name, zone, project_id]):
            return jsonify({
                'success': False,
                'error': 'Missing required parameters: vm_name, zone, project_id'
            }), 400
        
        result = db_pipeline.test_ssh_connection(vm_name, zone, project_id)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"Test SSH error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/dump-database', methods=['POST'])
def dump_database():
    """Dump database"""
    try:
        data = request.get_json()
        vm_name = data.get('vm_name')
        zone = data.get('zone')
        project_id = data.get('project_id')
        
        if not all([vm_name, zone, project_id]):
            return jsonify({
                'success': False,
                'error': 'Missing required parameters: vm_name, zone, project_id'
            }), 400
        
        result = db_pipeline.dump_database(vm_name, zone, project_id)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"Dump database error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/execute-query', methods=['POST'])
def execute_query():
    """Execute validation query"""
    try:
        data = request.get_json()
        vm_name = data.get('vm_name')
        zone = data.get('zone')
        project_id = data.get('project_id')
        
        if not all([vm_name, zone, project_id]):
            return jsonify({
                'success': False,
                'error': 'Missing required parameters: vm_name, zone, project_id'
            }), 400
        
        result = db_pipeline.execute_query(vm_name, zone, project_id)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"Execute query error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
