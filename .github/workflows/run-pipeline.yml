name: Run Data Pipeline (Cloud Run Orchestrated)

on:
  schedule:
    - cron: '0 2 * * 0'  # Weekly on Sunday at 2 AM UTC
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run pipeline in'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      pipeline_action:
        description: 'Pipeline action to perform'
        required: true
        default: 'run-cloud-orchestrated'
        type: choice
        options:
          - run-cloud-orchestrated
          - deploy-cloud-services
          - destroy-cloud-services
          - test-orchestrator-only

env:
  TF_VERSION: '1.6.0'
  TF_WORKING_DIR: './infrastructure/terraform'

jobs:
  debug-inputs:
    name: Debug Workflow Inputs
    runs-on: ubuntu-latest
    steps:
      - name: Display Workflow Inputs
        run: |
          echo "=== Cloud Run Orchestrated Pipeline Debug ==="
          echo "Triggered by: ${{ github.event_name }}"
          echo "Branch: ${{ github.ref }}"
          echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
          echo "Pipeline Action: ${{ github.event.inputs.pipeline_action || 'run-cloud-orchestrated' }}"
          echo ""
          echo "=== Job Execution Plan ==="
          if [ "${{ github.event.inputs.pipeline_action }}" == "run-cloud-orchestrated" ]; then
            echo "✅ Will trigger Cloud Run orchestrator for complete pipeline"
          elif [ "${{ github.event.inputs.pipeline_action }}" == "deploy-cloud-services" ]; then
            echo "✅ Will deploy Cloud Run services"
          elif [ "${{ github.event.inputs.pipeline_action }}" == "destroy-cloud-services" ]; then
            echo "✅ Will destroy Cloud Run services"
          elif [ "${{ github.event.inputs.pipeline_action }}" == "test-orchestrator-only" ]; then
            echo "✅ Will test orchestrator health only"
          fi
          echo "=== End Debug ==="

  deploy-cloud-services:
    name: Deploy Cloud Run Services
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      github.event.inputs.pipeline_action == 'deploy-cloud-services'

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Setup Docker
        run: |
          gcloud auth configure-docker europe-west10-docker.pkg.dev

      - name: Deploy Cloud Run Services
        run: |
          export GCP_PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          export GCP_REGION="${{ secrets.GCP_REGION }}"
          export ENVIRONMENT="${{ github.event.inputs.environment || 'dev' }}"

          chmod +x scripts/deploy-cloud-run-services.sh
          ./scripts/deploy-cloud-run-services.sh

  destroy-cloud-services:
    name: Destroy Cloud Run Services
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      github.event.inputs.pipeline_action == 'destroy-cloud-services'

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Destroy Cloud Run Services
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment || 'dev' }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          REGION="${{ secrets.GCP_REGION }}"

          echo "=== Destroying Cloud Run Services ==="

          # Delete Cloud Run services
          gcloud run services delete data-pipeline-${ENVIRONMENT}-orchestrator --region=$REGION --quiet || echo "Orchestrator service not found"
          gcloud run services delete data-pipeline-${ENVIRONMENT}-vm-manager --region=$REGION --quiet || echo "VM Manager service not found"
          gcloud run services delete data-pipeline-${ENVIRONMENT}-db-pipeline --region=$REGION --quiet || echo "Database Pipeline service not found"

          # Delete Artifact Registry repository
          gcloud artifacts repositories delete data-pipeline-${ENVIRONMENT}-repo --location=$REGION --quiet || echo "Repository not found"

          # Delete Cloud Scheduler job
          gcloud scheduler jobs delete data-pipeline-${ENVIRONMENT}-scheduler --location=$REGION --quiet || echo "Scheduler job not found"

          echo "=== Cloud Run Services Cleanup Completed ==="

  test-orchestrator:
    name: Test Cloud Run Orchestrator
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      github.event.inputs.pipeline_action == 'test-orchestrator-only'

    steps:
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Test Orchestrator Health
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment || 'dev' }}"
          REGION="${{ secrets.GCP_REGION }}"

          # Get orchestrator URL
          ORCHESTRATOR_URL=$(gcloud run services describe data-pipeline-${ENVIRONMENT}-orchestrator --region=$REGION --format="value(status.url)" 2>/dev/null || echo "")

          if [ -z "$ORCHESTRATOR_URL" ]; then
            echo "❌ Orchestrator service not found. Please deploy Cloud Run services first."
            exit 1
          fi

          echo "Testing orchestrator at: $ORCHESTRATOR_URL"

          # Test health endpoint
          if curl -f "$ORCHESTRATOR_URL/health"; then
            echo "✅ Orchestrator health check passed"
          else
            echo "❌ Orchestrator health check failed"
            exit 1
          fi

  run-cloud-orchestrated-pipeline:
    name: Execute Cloud Run Orchestrated Pipeline
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      github.event.inputs.pipeline_action == 'run-cloud-orchestrated'

    outputs:
      pipeline_status: ${{ steps.execute-pipeline.outputs.status }}
      pipeline_result: ${{ steps.execute-pipeline.outputs.result }}

    steps:
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Get Orchestrator URL
        id: get-orchestrator
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment || 'dev' }}"
          REGION="${{ secrets.GCP_REGION }}"

          # Get orchestrator URL
          ORCHESTRATOR_URL=$(gcloud run services describe data-pipeline-${ENVIRONMENT}-orchestrator --region=$REGION --format="value(status.url)" 2>/dev/null || echo "")

          if [ -z "$ORCHESTRATOR_URL" ]; then
            echo "❌ Orchestrator service not found. Please deploy Cloud Run services first using 'deploy-cloud-services' action."
            exit 1
          fi

          echo "orchestrator_url=$ORCHESTRATOR_URL" >> $GITHUB_OUTPUT
          echo "Found orchestrator at: $ORCHESTRATOR_URL"

      - name: Execute Pipeline via Cloud Run
        id: execute-pipeline
        run: |
          ORCHESTRATOR_URL="${{ steps.get-orchestrator.outputs.orchestrator_url }}"
          PIPELINE_ID="github-${{ github.event.inputs.environment || 'dev' }}-$(date +%Y%m%d-%H%M%S)"

          echo "=== Executing Cloud Run Orchestrated Pipeline ==="
          echo "Pipeline ID: $PIPELINE_ID"
          echo "Orchestrator URL: $ORCHESTRATOR_URL"

          # Create pipeline request payload
          PAYLOAD=$(cat << EOF
          {
            "pipeline_id": "$PIPELINE_ID",
            "source": "github_actions",
            "environment": "${{ github.event.inputs.environment || 'dev' }}",
            "triggered_by": "${{ github.actor }}",
            "workflow_run_id": "${{ github.run_id }}"
          }
          EOF
          )

          echo "Request payload:"
          echo "$PAYLOAD"

          # Execute pipeline
          echo "Triggering pipeline execution..."
          RESPONSE=$(curl -s -X POST "$ORCHESTRATOR_URL/execute-pipeline" \
            -H "Content-Type: application/json" \
            -d "$PAYLOAD" \
            --max-time 3600 \
            --fail-with-body) || {
            echo "❌ Pipeline execution failed"
            echo "Response: $RESPONSE"
            exit 1
          }

          echo "=== Pipeline Response ==="
          echo "$RESPONSE"

          # Parse response
          STATUS=$(echo "$RESPONSE" | jq -r '.status // "unknown"')
          RESULT=$(echo "$RESPONSE" | jq -r '.result // "none"')

          echo "status=$STATUS" >> $GITHUB_OUTPUT
          echo "result=$RESULT" >> $GITHUB_OUTPUT

          if [ "$STATUS" = "completed" ]; then
            echo "✅ Pipeline completed successfully!"
            echo "Final result: $RESULT"
          elif [ "$STATUS" = "failed" ]; then
            echo "❌ Pipeline failed!"
            ERROR=$(echo "$RESPONSE" | jq -r '.errors[0] // "Unknown error"')
            echo "Error: $ERROR"
            exit 1
          else
            echo "⚠️ Pipeline status: $STATUS"
          fi

      - name: Pipeline Summary
        run: |
          echo "=== Cloud Run Orchestrated Pipeline Summary ==="
          echo "Pipeline ID: github-${{ github.event.inputs.environment || 'dev' }}-$(date +%Y%m%d-%H%M%S)"
          echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
          echo "Status: ${{ steps.execute-pipeline.outputs.status }}"
          echo "Result: ${{ steps.execute-pipeline.outputs.result }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Workflow run: ${{ github.run_id }}"
          echo ""
          echo "✅ Pipeline execution completed via Cloud Run orchestration"
          echo "🔄 All VM lifecycle management handled automatically by Cloud Run services"
          echo "📊 Database operations executed and validated successfully"




