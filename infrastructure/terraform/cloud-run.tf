# Cloud Run services for pipeline orchestration

# Enable required APIs
resource "google_project_service" "cloud_run_api" {
  service = "run.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloud_build_api" {
  service = "cloudbuild.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "artifact_registry_api" {
  service = "artifactregistry.googleapis.com"
  disable_on_destroy = false
}

# Artifact Registry for container images
resource "google_artifact_registry_repository" "pipeline_repo" {
  location      = var.region
  repository_id = "${var.project_name}-${var.environment}-repo"
  description   = "Container repository for pipeline services"
  format        = "DOCKER"
  
  depends_on = [google_project_service.artifact_registry_api]
}

# Cloud Run service for Pipeline Orchestrator
resource "google_cloud_run_service" "pipeline_orchestrator" {
  name     = "${var.project_name}-${var.environment}-orchestrator"
  location = var.region

  template {
    spec {
      containers {
        image = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.pipeline_repo.repository_id}/orchestrator:latest"
        
        ports {
          container_port = 8080
        }
        
        env {
          name  = "GCP_PROJECT_ID"
          value = var.project_id
        }
        
        env {
          name  = "GCP_REGION"
          value = var.region
        }
        
        env {
          name  = "GCP_ZONE"
          value = var.zone
        }
        
        env {
          name  = "VM_MANAGER_URL"
          value = google_cloud_run_service.vm_manager.status[0].url
        }
        
        env {
          name  = "DB_PIPELINE_URL"
          value = google_cloud_run_service.database_pipeline.status[0].url
        }
        
        env {
          name  = "AWS_HOSTNAME"
          value = var.aws_hostname
        }
        
        env {
          name  = "AWS_USER"
          value = var.aws_user
        }
        
        env {
          name  = "AWS_PRIVATE_KEY"
          value = var.aws_private_key
        }
        
        env {
          name  = "AWS_PUBLIC_KEY"
          value = var.aws_public_key
        }
        
        env {
          name  = "GITHUB_REPO"
          value = var.github_repo
        }
        
        env {
          name  = "GITHUB_TOKEN"
          value = var.github_token
        }
        
        env {
          name  = "VM_MACHINE_TYPE"
          value = var.machine_type
        }
        
        env {
          name  = "VM_DISK_SIZE"
          value = tostring(var.disk_size)
        }
        
        env {
          name  = "ENVIRONMENT"
          value = var.environment
        }
        
        resources {
          limits = {
            cpu    = "2"
            memory = "2Gi"
          }
        }
      }
      
      service_account_name = local.existing_service_account_email
      timeout_seconds      = 3600  # 1 hour timeout
    }
    
    metadata {
      annotations = {
        "autoscaling.knative.dev/maxScale" = "10"
        "run.googleapis.com/execution-environment" = "gen2"
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }
  
  depends_on = [google_project_service.cloud_run_api]
}

# Cloud Run service for VM Manager
resource "google_cloud_run_service" "vm_manager" {
  name     = "${var.project_name}-${var.environment}-vm-manager"
  location = var.region

  template {
    spec {
      containers {
        image = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.pipeline_repo.repository_id}/vm-manager:latest"
        
        ports {
          container_port = 8080
        }
        
        resources {
          limits = {
            cpu    = "2"
            memory = "2Gi"
          }
        }
      }
      
      service_account_name = local.existing_service_account_email
      timeout_seconds      = 1800  # 30 minutes timeout
    }
    
    metadata {
      annotations = {
        "autoscaling.knative.dev/maxScale" = "5"
        "run.googleapis.com/execution-environment" = "gen2"
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }
  
  depends_on = [google_project_service.cloud_run_api]
}

# Cloud Run service for Database Pipeline
resource "google_cloud_run_service" "database_pipeline" {
  name     = "${var.project_name}-${var.environment}-db-pipeline"
  location = var.region

  template {
    spec {
      containers {
        image = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.pipeline_repo.repository_id}/database-pipeline:latest"
        
        ports {
          container_port = 8080
        }
        
        resources {
          limits = {
            cpu    = "1"
            memory = "1Gi"
          }
        }
      }
      
      service_account_name = local.existing_service_account_email
      timeout_seconds      = 1800  # 30 minutes timeout
    }
    
    metadata {
      annotations = {
        "autoscaling.knative.dev/maxScale" = "5"
        "run.googleapis.com/execution-environment" = "gen2"
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }
  
  depends_on = [google_project_service.cloud_run_api]
}

# IAM bindings for Cloud Run services
resource "google_cloud_run_service_iam_binding" "orchestrator_invoker" {
  location = google_cloud_run_service.pipeline_orchestrator.location
  service  = google_cloud_run_service.pipeline_orchestrator.name
  role     = "roles/run.invoker"
  members = [
    "serviceAccount:${local.existing_service_account_email}",
    "allUsers"  # For GitHub Actions to invoke
  ]
}

resource "google_cloud_run_service_iam_binding" "vm_manager_invoker" {
  location = google_cloud_run_service.vm_manager.location
  service  = google_cloud_run_service.vm_manager.name
  role     = "roles/run.invoker"
  members = [
    "serviceAccount:${local.existing_service_account_email}"
  ]
}

resource "google_cloud_run_service_iam_binding" "database_pipeline_invoker" {
  location = google_cloud_run_service.database_pipeline.location
  service  = google_cloud_run_service.database_pipeline.name
  role     = "roles/run.invoker"
  members = [
    "serviceAccount:${local.existing_service_account_email}"
  ]
}

# Cloud Scheduler job for automated pipeline execution
resource "google_cloud_scheduler_job" "pipeline_scheduler" {
  count = var.pipeline_schedule != "" ? 1 : 0
  
  name             = "${var.project_name}-${var.environment}-scheduler"
  description      = "Automated pipeline execution"
  schedule         = var.pipeline_schedule
  time_zone        = "UTC"
  attempt_deadline = "3600s"

  http_target {
    http_method = "POST"
    uri         = "${google_cloud_run_service.pipeline_orchestrator.status[0].url}/execute-pipeline"
    
    headers = {
      "Content-Type" = "application/json"
    }
    
    body = base64encode(jsonencode({
      pipeline_id = "scheduled-${var.environment}-${formatdate("YYYYMMDD-hhmm", timestamp())}"
      source      = "scheduler"
    }))
    
    oidc_token {
      service_account_email = local.existing_service_account_email
    }
  }
  
  depends_on = [google_project_service.cloud_run_api]
}
