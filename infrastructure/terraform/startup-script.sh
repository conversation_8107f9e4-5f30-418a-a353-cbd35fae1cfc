#!/bin/bash

# Data Pipeline VM Startup Script
# This script configures SSH keys, installs dependencies, and tests AWS EC2 connectivity

set -e

# Logging setup
LOG_FILE="/var/log/pipeline-startup.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "=== Data Pipeline VM Startup Script Started at $(date) ==="

# Update system packages
echo "Updating system packages..."
apt-get update -y
apt-get upgrade -y

# Install required packages
echo "Installing required packages..."
apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    git \
    curl \
    wget \
    unzip \
    openssh-client \
    jq \
    htop \
    vim \
    nano

# Handle MySQL/MariaDB installation separately due to potential conflicts
echo "Setting up database client..."
# First try to install mysql-client (usually works)
if apt-get install -y mysql-client; then
    echo "MySQL client installed successfully"
    DB_CLIENT="mysql"
else
    echo "MySQL client installation failed, trying alternative approach..."
    # If MySQL client fails, just use the default mysql command if available
    if command -v mysql &> /dev/null; then
        echo "MySQL command already available"
        DB_CLIENT="mysql"
    else
        echo "No MySQL client available, will skip database operations"
        DB_CLIENT="none"
    fi
fi

# Install Google Cloud SDK (if not already installed)
if ! command -v gcloud &> /dev/null; then
    echo "Installing Google Cloud SDK..."
    curl https://sdk.cloud.google.com | bash
    source /root/.bashrc
fi

# Create pipeline user
echo "Creating pipeline user..."
useradd -m -s /bin/bash pipeline || echo "User pipeline already exists"
usermod -aG sudo pipeline

# Setup database (if available)
if [ "$DB_CLIENT" != "none" ]; then
    echo "Setting up database..."

    # Check if MySQL server is running or can be started
    if systemctl is-active --quiet mysql || systemctl start mysql 2>/dev/null; then
        echo "MySQL server is running"

        # Try to secure MySQL installation (automated)
        echo "Configuring MySQL..."
        mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'pipeline123';" 2>/dev/null || \
        mysql -e "UPDATE mysql.user SET authentication_string = PASSWORD('pipeline123') WHERE User = 'root';" 2>/dev/null || \
        echo "MySQL root password setup skipped (may already be configured)"

        mysql -e "DELETE FROM mysql.user WHERE User='';" 2>/dev/null || true
        mysql -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');" 2>/dev/null || true
        mysql -e "DROP DATABASE IF EXISTS test;" 2>/dev/null || true
        mysql -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';" 2>/dev/null || true
        mysql -e "FLUSH PRIVILEGES;" 2>/dev/null || true

        # Create pipeline database and user
        echo "Creating pipeline database..."
        mysql -u root -ppipeline123 -e "CREATE DATABASE IF NOT EXISTS pipeline_data;" 2>/dev/null || \
        mysql -u root -e "CREATE DATABASE IF NOT EXISTS pipeline_data;" 2>/dev/null || \
        echo "Database creation skipped"

        mysql -u root -ppipeline123 -e "CREATE USER IF NOT EXISTS 'pipeline'@'localhost' IDENTIFIED BY 'pipeline123';" 2>/dev/null || \
        mysql -u root -e "CREATE USER IF NOT EXISTS 'pipeline'@'localhost' IDENTIFIED BY 'pipeline123';" 2>/dev/null || \
        echo "User creation skipped"

        mysql -u root -ppipeline123 -e "GRANT ALL PRIVILEGES ON pipeline_data.* TO 'pipeline'@'localhost';" 2>/dev/null || \
        mysql -u root -e "GRANT ALL PRIVILEGES ON pipeline_data.* TO 'pipeline'@'localhost';" 2>/dev/null || \
        echo "Privileges grant skipped"

        mysql -e "FLUSH PRIVILEGES;" 2>/dev/null || true
        echo "Database setup completed"
    else
        echo "No MySQL server available, skipping database setup"
    fi
else
    echo "No database client available, skipping database setup"
fi

# Switch to pipeline user for SSH setup
sudo -u pipeline bash << 'EOF'
set -e

# Create SSH directory
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Create private key file
echo "Setting up SSH private key..."
cat > /home/<USER>/.ssh/aws_private_key << 'PRIVATE_KEY_EOF'
${private_key_content}
PRIVATE_KEY_EOF

# Create public key file
echo "Setting up SSH public key..."
cat > /home/<USER>/.ssh/aws_public_key << 'PUBLIC_KEY_EOF'
${public_key_content}
PUBLIC_KEY_EOF

# Set proper permissions for SSH keys
chmod 600 /home/<USER>/.ssh/aws_private_key
chmod 644 /home/<USER>/.ssh/aws_public_key

# Create SSH config
echo "Creating SSH configuration..."
cat > /home/<USER>/.ssh/config << 'SSH_CONFIG_EOF'
Host trips
    HostName ${aws_hostname}
    User ${aws_user}
    IdentityFile ~/.ssh/aws_private_key
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout 30
SSH_CONFIG_EOF

chmod 600 /home/<USER>/.ssh/config

# Start SSH agent and add key
echo "Starting SSH agent and adding private key..."
eval "$(ssh-agent -s)"

# Save SSH agent environment variables for later use
echo "export SSH_AUTH_SOCK=$SSH_AUTH_SOCK" > /home/<USER>/.ssh/ssh_agent_env
echo "export SSH_AGENT_PID=$SSH_AGENT_PID" >> /home/<USER>/.ssh/ssh_agent_env
chmod 600 /home/<USER>/.ssh/ssh_agent_env

# Check if ssh-agent is running
if ssh-add -l &>/dev/null; then
    echo "SSH agent is running"
else
    echo "Starting SSH agent..."
    eval "$(ssh-agent -s)"
    echo "export SSH_AUTH_SOCK=$SSH_AUTH_SOCK" > /home/<USER>/.ssh/ssh_agent_env
    echo "export SSH_AGENT_PID=$SSH_AGENT_PID" >> /home/<USER>/.ssh/ssh_agent_env
    chmod 600 /home/<USER>/.ssh/ssh_agent_env
fi

# Add private key to ssh-agent
echo "Adding private key to SSH agent..."
ssh-add /home/<USER>/.ssh/aws_private_key

# List keys in ssh-agent
echo "Keys in SSH agent:"
ssh-add -l

# Create a script to easily reload SSH agent environment
cat > /home/<USER>/.ssh/load_ssh_agent.sh << 'LOAD_SSH_EOF'
#!/bin/bash
# Load SSH agent environment and add key if needed
if [ -f ~/.ssh/ssh_agent_env ]; then
    source ~/.ssh/ssh_agent_env
    # Check if agent is still running and key is loaded
    if ! ssh-add -l &>/dev/null; then
        echo "SSH agent not running or no keys loaded, starting new agent..."
        eval "$(ssh-agent -s)"
        echo "export SSH_AUTH_SOCK=$SSH_AUTH_SOCK" > ~/.ssh/ssh_agent_env
        echo "export SSH_AGENT_PID=$SSH_AGENT_PID" >> ~/.ssh/ssh_agent_env
        ssh-add ~/.ssh/aws_private_key
    fi
else
    echo "Starting new SSH agent..."
    eval "$(ssh-agent -s)"
    echo "export SSH_AUTH_SOCK=$SSH_AUTH_SOCK" > ~/.ssh/ssh_agent_env
    echo "export SSH_AGENT_PID=$SSH_AGENT_PID" >> ~/.ssh/ssh_agent_env
    ssh-add ~/.ssh/aws_private_key
fi
LOAD_SSH_EOF

chmod +x /home/<USER>/.ssh/load_ssh_agent.sh

# Add SSH agent loading to .bashrc
echo "# Load SSH agent for AWS connections" >> /home/<USER>/.bashrc
echo "source ~/.ssh/load_ssh_agent.sh" >> /home/<USER>/.bashrc

# Verify SSH key setup
echo "Verifying SSH key configuration..."
echo "Private key file size: $(wc -c < /home/<USER>/.ssh/aws_private_key) bytes"
echo "Public key file size: $(wc -c < /home/<USER>/.ssh/aws_public_key) bytes"
echo "Private key fingerprint:"
ssh-keygen -lf /home/<USER>/.ssh/aws_private_key || echo "Could not generate fingerprint"

# Test SSH connection to AWS EC2
echo "Testing SSH connection to AWS EC2..."
SSH_OUTPUT_FILE="/home/<USER>/ssh_test_output.log"

# Test the SSH connection and capture output
echo "=== SSH Connection Test Started at $(date) ===" > "$SSH_OUTPUT_FILE"

if timeout 30 ssh -o ConnectTimeout=10 -o BatchMode=yes trips "echo 'SSH connection successful'; hostname; whoami; date" >> "$SSH_OUTPUT_FILE" 2>&1; then
    echo "SSH connection to AWS EC2 successful!" | tee -a "$SSH_OUTPUT_FILE"
    
    # Run additional commands to gather system info
    echo "=== Gathering AWS EC2 System Information ===" >> "$SSH_OUTPUT_FILE"
    ssh trips "
        echo 'System Information:';
        uname -a;
        echo 'Disk Usage:';
        df -h;
        echo 'Memory Usage:';
        free -h;
        echo 'MySQL/MariaDB Status:';
        systemctl status mysql || systemctl status mariadb || echo 'MySQL/MariaDB not running';
        echo 'Available Databases:';
        mysql -e 'SHOW DATABASES;' 2>/dev/null || echo 'Cannot connect to MySQL/MariaDB';
    " >> "$SSH_OUTPUT_FILE" 2>&1
    
    echo "AWS EC2 system information gathered successfully"
else
    echo "SSH connection to AWS EC2 failed!" | tee -a "$SSH_OUTPUT_FILE"
    echo "Connection details:" >> "$SSH_OUTPUT_FILE"
    echo "Hostname: ${aws_hostname}" >> "$SSH_OUTPUT_FILE"
    echo "User: ${aws_user}" >> "$SSH_OUTPUT_FILE"
    echo "Private key path: /home/<USER>/.ssh/aws_private_key" >> "$SSH_OUTPUT_FILE"
fi

echo "=== SSH Connection Test Completed at $(date) ===" >> "$SSH_OUTPUT_FILE"

# Display the SSH test output in the startup log
echo "=== SSH Test Output ==="
cat "$SSH_OUTPUT_FILE"
echo "=== End SSH Test Output ==="

EOF

# Clone GitHub repository if provided
if [ -n "${github_repo}" ] && [ -n "${github_token}" ]; then
    echo "Cloning GitHub repository..."
    sudo -u pipeline bash << 'EOF'
    cd /home/<USER>

    # Configure git with token authentication
    REPO_URL="${github_repo}"
    if [[ "$REPO_URL" == https://github.com/* ]]; then
        # Insert token into HTTPS URL
        REPO_WITH_TOKEN=$(echo "$REPO_URL" | sed "s|https://github.com/|https://${github_token}@github.com/|")
        git clone "$REPO_WITH_TOKEN" pipeline-repo
    else
        git clone "$REPO_URL" pipeline-repo
    fi

    cd pipeline-repo
    git checkout main || git checkout master

    # Copy VM pipeline runner to home directory
    if [ -f "scripts/vm_pipeline_runner.py" ]; then
        cp scripts/vm_pipeline_runner.py /home/<USER>/
        chmod +x /home/<USER>/vm_pipeline_runner.py
        echo "VM pipeline runner copied and made executable"
    fi

    echo "Repository cloned successfully to /home/<USER>/pipeline-repo"
EOF
fi

# Install Python dependencies if requirements.txt exists
if [ -f "/home/<USER>/pipeline-repo/requirements.txt" ]; then
    echo "Installing Python dependencies..."
    sudo -u pipeline bash << 'EOF'
    cd /home/<USER>/pipeline-repo
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
EOF
fi

# Database configuration was already handled earlier in the script

# Create a summary file with important information
sudo -u pipeline bash << 'EOF'
cat > /home/<USER>/vm_setup_summary.txt << 'SUMMARY_EOF'
=== Data Pipeline VM Setup Summary ===
Setup completed at: $(date)

SSH Configuration:
- Private key: /home/<USER>/.ssh/aws_private_key
- Public key: /home/<USER>/.ssh/aws_public_key
- SSH config: /home/<USER>/.ssh/config
- SSH alias: trips (connects to ${aws_hostname} as ${aws_user})

Test Results:
- SSH test output: /home/<USER>/ssh_test_output.log

Database Configuration:
- MySQL/MariaDB: Available if installed
- Root password: pipeline123 (if configured)
- Pipeline database: pipeline_data (if created)
- Pipeline user: pipeline (password: pipeline123, if created)

Repository:
- Location: /home/<USER>/pipeline-repo (if cloned)
- Python virtual environment: /home/<USER>/pipeline-repo/venv (if created)

Log Files:
- Startup log: /var/log/pipeline-startup.log
- SSH test log: /home/<USER>/ssh_test_output.log

Commands to test SSH:
- ssh trips
- ssh ${aws_user}@${aws_hostname}

Next Steps:
1. Check SSH connectivity: ssh trips
2. Run data pipeline scripts
3. Monitor logs in /var/log/pipeline-startup.log
SUMMARY_EOF
EOF

# Send logs to Google Cloud Logging
if command -v gcloud &> /dev/null; then
    echo "Sending startup logs to Google Cloud Logging..."
    gcloud logging write pipeline-vm-startup "VM startup completed successfully" --severity=INFO
    
    # Send SSH test results to logging
    if [ -f "/home/<USER>/ssh_test_output.log" ]; then
        gcloud logging write pipeline-vm-ssh-test "$(cat /home/<USER>/ssh_test_output.log)" --severity=INFO
    fi
fi

# Print completion message multiple times to ensure it's captured
echo "Data Pipeline VM Startup Script Completed Successfully"
echo "=== Data Pipeline VM Startup Script Completed Successfully at $(date) ==="
echo "Data Pipeline VM Startup Script Completed Successfully"

# Flush output to ensure it reaches the serial console
sync
sleep 2

# Print again to be absolutely sure
echo "STARTUP_COMPLETE: Data Pipeline VM Startup Script Completed Successfully"

echo "Check /home/<USER>/vm_setup_summary.txt for setup details"
echo "Check /home/<USER>/ssh_test_output.log for SSH test results"

# Final flush
sync
