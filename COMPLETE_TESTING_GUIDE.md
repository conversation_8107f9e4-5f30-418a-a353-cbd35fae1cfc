# Complete Testing Guide for Cloud Run Orchestration

## Prerequisites

### 1. Required IAM Roles for Service Account
Add these roles to `<EMAIL>`:

**Critical Missing Roles:**
```bash
# Add these through GCP Console (IAM & Admin > IAM)
roles/artifactregistry.admin          # Create/manage Artifact Registry
roles/iam.serviceAccountAdmin         # Manage service accounts  
roles/resourcemanager.projectIamAdmin # Manage IAM bindings
```

**Current Roles (Already Assigned):**
- ✅ Cloud Run developer
- ✅ Cloud Scheduler Admin  
- ✅ Compute Admin
- ✅ Compute Storage Admin
- ✅ Logging Admin
- ✅ Monitoring Admin
- ✅ Service Account User
- ✅ Storage Admin

### 2. GitHub Token Permissions
Update your GitHub token with repository access:
1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Edit token: `*********************************************************************************************`
3. Grant access to: `travel-buddies/gcp_data_pipeline_tools_ingestion`
4. Permissions: Contents (Read), Metadata (Read), Pull requests (Read)

## Testing Steps

### Phase 1: Add IAM Roles (Manual)
Since the service account can't modify its own permissions, add these roles manually:

1. **Go to GCP Console**: https://console.cloud.google.com/iam-admin/iam?project=external-data-source-437915
2. **Find service account**: `<EMAIL>`
3. **Click Edit** and add these roles:
   - Artifact Registry Admin
   - Service Account Admin  
   - Project IAM Admin

### Phase 2: Deploy Full Infrastructure
```bash
# Navigate to project root
cd /Users/<USER>/Documents/Project/gcp_data_pipeline_tools_ingestion

# Deploy with authentication
./scripts/deploy-with-auth.sh
```

### Phase 3: Build and Deploy Cloud Run Services
```bash
# Build container images and deploy services
./scripts/deploy-cloud-run.sh build

# Deploy Cloud Run services
./scripts/deploy-cloud-run.sh deploy

# Get service URLs
./scripts/deploy-cloud-run.sh urls
```

### Phase 4: Test VM SSH and Database Operations

#### 4.1 Test VM SSH Connection
```bash
# SSH into the VM
gcloud compute ssh data-pipeline-dev-pipeline-vm --zone=europe-west3-a --project=external-data-source-437915

# Check startup script logs
sudo journalctl -u google-startup-scripts.service -f

# Test SSH to AWS EC2
ssh -i /home/<USER>/.ssh/aws_key forge@***********
```

#### 4.2 Test Database Operations
```bash
# On the VM, test MySQL connection to AWS
mysql -h *********** -u your_username -p

# Test a simple query
SELECT 1 as test_connection;

# Test mysqldump
mysqldump -h *********** -u your_username -p database_name > test_dump.sql

# Verify dump file
head -20 test_dump.sql
```

### Phase 5: Test Cloud Run Orchestration

#### 5.1 Test VM Manager Service
```bash
# Get VM Manager URL
VM_MANAGER_URL=$(terraform output -raw vm_manager_url)

# Test health endpoint
curl "${VM_MANAGER_URL}/health"

# Test VM creation
curl -X POST "${VM_MANAGER_URL}/vm/create" \
  -H "Content-Type: application/json" \
  -d '{"operation": "create"}'
```

#### 5.2 Test Database Pipeline Service
```bash
# Get Database Pipeline URL
DATABASE_PIPELINE_URL=$(terraform output -raw database_pipeline_url)

# Test health endpoint
curl "${DATABASE_PIPELINE_URL}/health"

# Test database operation
curl -X POST "${DATABASE_PIPELINE_URL}/pipeline/run" \
  -H "Content-Type: application/json" \
  -d '{"operation": "dump_and_import"}'
```

#### 5.3 Test Full Orchestration
```bash
# Get Orchestrator URL
ORCHESTRATOR_URL=$(terraform output -raw orchestrator_url)

# Test health endpoint
curl "${ORCHESTRATOR_URL}/health"

# Run complete pipeline
curl -X POST "${ORCHESTRATOR_URL}/pipeline/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "steps": ["create_vm", "ssh_setup", "database_dump", "mysql_import", "test_query", "cleanup"]
  }'
```

### Phase 6: Test GitHub Actions Integration

#### 6.1 Manual Trigger Test
1. Go to GitHub Actions: https://github.com/travel-buddies/gcp_data_pipeline_tools_ingestion/actions
2. Find "Deploy Data Pipeline Infrastructure" workflow
3. Click "Run workflow"
4. Select branch and parameters
5. Monitor execution

#### 6.2 Test SSH Key Integration
```bash
# Verify GitHub secrets are set
# AWS_PRIVATE_KEY and AWS_PUBLIC_KEY should be configured in repository secrets
```

## Expected Results

### Successful Deployment Should Show:
```
✅ Artifact Registry repository created
✅ VM infrastructure deployed
✅ Cloud Run services deployed and healthy
✅ Service URLs accessible
✅ VM can SSH to AWS EC2
✅ Database operations working
✅ Full orchestration pipeline functional
```

### Service URLs Format:
```
VM Manager:        https://vm-manager-xxx-ew.a.run.app
Database Pipeline: https://database-pipeline-xxx-ew.a.run.app  
Orchestrator:      https://pipeline-orchestrator-xxx-ew.a.run.app
```

## Troubleshooting

### Common Issues:
1. **Permission Denied**: Ensure all IAM roles are added
2. **Container Not Found**: Build and push images first
3. **SSH Connection Failed**: Check AWS EC2 security groups
4. **Database Connection Failed**: Verify MySQL credentials and network access

### Debug Commands:
```bash
# Check Cloud Run logs
gcloud logging read "resource.type=cloud_run_revision" --limit=50

# Check VM logs  
gcloud compute instances get-serial-port-output data-pipeline-dev-pipeline-vm --zone=europe-west3-a

# Check Terraform state
terraform state list
terraform show
```

## Cleanup
```bash
# Destroy all resources
cd infrastructure/terraform
terraform destroy -auto-approve
```

This guide provides a complete testing framework for your Cloud Run orchestration with SSH keys, database operations, and MySQL testing.
