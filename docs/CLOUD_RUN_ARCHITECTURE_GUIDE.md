# Cloud Run Orchestrated Data Pipeline Architecture Guide

## Overview

This guide explains the new Cloud Run-based architecture that automates the complete data pipeline lifecycle, reducing GitHub Actions load and providing better scalability and monitoring.

## Architecture Components

### 1. Cloud Run Services

#### Pipeline Orchestrator (`data-pipeline-{env}-orchestrator`)
- **Purpose**: Main coordinator for the entire pipeline process
- **Responsibilities**:
  - Receives pipeline execution requests
  - Coordinates VM lifecycle through VM Manager
  - Manages database operations through Database Pipeline service
  - Provides status tracking and error handling
- **Endpoints**:
  - `POST /execute-pipeline` - Trigger pipeline execution
  - `GET /health` - Health check
  - `GET /pipeline-status/{id}` - Get pipeline status

#### VM Manager (`data-pipeline-{env}-vm-manager`)
- **Purpose**: Handles all VM lifecycle operations
- **Responsibilities**:
  - Creates VMs using Terraform
  - Monitors VM startup completion
  - Manages VM cleanup and resource deletion
- **Endpoints**:
  - `POST /create-vm` - Create and configure VM
  - `POST /wait-startup` - Wait for VM startup completion
  - `POST /cleanup-vm` - Cleanup VM and resources

#### Database Pipeline (`data-pipeline-{env}-db-pipeline`)
- **Purpose**: Executes database operations via SSH
- **Responsibilities**:
  - Tests SSH connectivity to AWS EC2
  - Performs database dumps (schema and complete data)
  - Executes validation queries
  - Handles retry logic for network operations
- **Endpoints**:
  - `POST /test-ssh` - Test SSH connection
  - `POST /dump-database` - Dump database
  - `POST /execute-query` - Execute validation query

### 2. Complete Pipeline Flow

```mermaid
graph TD
    A[GitHub Actions] --> B[Pipeline Orchestrator]
    B --> C[VM Manager: Create VM]
    C --> D[VM Manager: Wait for Startup]
    D --> E[Database Pipeline: Test SSH]
    E --> F[Database Pipeline: Dump Database]
    F --> G[Database Pipeline: Execute Query]
    G --> H[VM Manager: Cleanup VM]
    H --> I[Pipeline Complete]
    
    B --> J[Error Handling & Retry Logic]
    J --> K[Monitoring & Alerting]
```

## Deployment Guide

### Prerequisites

1. **GCP Project Setup**:
   ```bash
   export GCP_PROJECT_ID="your-project-id"
   export GCP_REGION="europe-west10"
   export ENVIRONMENT="dev"
   ```

2. **Required APIs**:
   - Cloud Run API
   - Cloud Build API
   - Artifact Registry API
   - Compute Engine API
   - Cloud Logging API
   - Cloud Monitoring API

3. **Service Account**:
   - Use existing: `vm-cuba-buddy-data-ingestion@{project}.iam.gserviceaccount.com`
   - Required roles: Cloud Run Developer, Compute Admin, Storage Admin, Logging Admin

### Step 1: Deploy Infrastructure

```bash
# Set up environment variables
export TF_VAR_project_id="your-project-id"
export TF_VAR_aws_hostname="your-aws-ec2-ip"
export TF_VAR_aws_user="forge"
export TF_VAR_aws_private_key="your-private-key"
export TF_VAR_aws_public_key="your-public-key"
export TF_VAR_github_token="your-github-token"

# Deploy Terraform infrastructure
cd infrastructure/terraform
terraform init
terraform apply -auto-approve
```

### Step 2: Deploy Cloud Run Services

```bash
# Deploy all Cloud Run services
chmod +x scripts/deploy-cloud-run-services.sh
./scripts/deploy-cloud-run-services.sh
```

### Step 3: Setup Monitoring (Optional)

```bash
# Setup monitoring and alerting
python monitoring/pipeline-monitoring.py
```

## Usage

### 1. GitHub Actions Workflow

The simplified GitHub Actions workflow now only triggers the Cloud Run orchestrator:

```yaml
# Trigger via GitHub Actions
- name: Execute Pipeline via Cloud Run
  run: |
    curl -X POST "$ORCHESTRATOR_URL/execute-pipeline" \
      -H "Content-Type: application/json" \
      -d '{"pipeline_id":"github-dev-20240630-1200"}'
```

### 2. Manual Execution

```bash
# Get orchestrator URL
ORCHESTRATOR_URL=$(gcloud run services describe data-pipeline-dev-orchestrator \
  --region=europe-west10 --format="value(status.url)")

# Trigger pipeline
curl -X POST "$ORCHESTRATOR_URL/execute-pipeline" \
  -H "Content-Type: application/json" \
  -d '{
    "pipeline_id": "manual-'$(date +%Y%m%d-%H%M%S)'",
    "source": "manual"
  }'
```

### 3. Local Testing

```bash
# Test complete Cloud Run architecture locally
python local_test_setup.py full

# Deploy only Cloud Run services
python local_test_setup.py deploy-cloud-run

# Test orchestrator only
python local_test_setup.py test-orchestrator
```

## Pipeline Process Details

### Automated VM Lifecycle

1. **VM Creation**:
   - Creates GCP VM with predefined configuration
   - Installs required dependencies (Python, MySQL client, SSH tools)
   - Configures SSH keys for AWS EC2 access
   - Clones GitHub repository with pipeline scripts

2. **SSH Configuration**:
   - Sets up SSH agent with AWS private key
   - Creates SSH alias for AWS EC2 connection
   - Tests connectivity before proceeding

3. **Database Operations**:
   - Dumps database schema (structure only)
   - Dumps complete database with data
   - Executes validation query: `SELECT COUNT(*) FROM trips WHERE is_booker_version=1`
   - Logs all operations for monitoring

4. **Resource Cleanup**:
   - Automatically deletes VM after completion
   - Removes all associated resources (disks, networks)
   - Ensures cost optimization

### Error Handling & Retry Logic

- **SSH Connection**: 3 retries with 10-second delays
- **Database Operations**: Automatic retry on network failures
- **VM Operations**: Graceful fallback to direct cleanup if Terraform fails
- **Timeout Management**: Appropriate timeouts for each operation phase

### Monitoring & Logging

- **Custom Metrics**: Pipeline duration, success/failure counts, SSH failures
- **Log-based Metrics**: Error tracking across all services
- **Health Checks**: Service availability monitoring
- **Alert Policies**: Automatic notifications on failures

## Benefits of Cloud Run Architecture

### 1. Reduced GitHub Actions Load
- GitHub Actions only triggers the orchestrator
- All heavy operations moved to Cloud Run
- Better resource utilization and cost control

### 2. Improved Scalability
- Cloud Run auto-scales based on demand
- Independent scaling for each service component
- Better handling of concurrent pipeline executions

### 3. Enhanced Monitoring
- Comprehensive logging across all components
- Custom metrics for pipeline performance
- Real-time health status monitoring

### 4. Better Error Handling
- Retry logic for transient failures
- Graceful degradation on partial failures
- Detailed error reporting and tracking

### 5. Cost Optimization
- Pay-per-use Cloud Run pricing
- Automatic VM cleanup after completion
- Efficient resource allocation

## Troubleshooting

### Common Issues

1. **Orchestrator Not Found**:
   ```bash
   # Check if services are deployed
   gcloud run services list --region=europe-west10
   
   # Deploy if missing
   ./scripts/deploy-cloud-run-services.sh
   ```

2. **SSH Connection Failures**:
   - Verify AWS EC2 security groups allow SSH from GCP
   - Check SSH key format and permissions
   - Review VM startup logs for SSH configuration issues

3. **Database Dump Failures**:
   - Verify MySQL access permissions on AWS EC2
   - Check network connectivity between GCP and AWS
   - Review database service status on AWS EC2

4. **VM Creation Failures**:
   - Check GCP quotas and limits
   - Verify service account permissions
   - Review Terraform state and configuration

### Monitoring Commands

```bash
# Check service health
curl $ORCHESTRATOR_URL/health

# View service logs
gcloud logs read "resource.type=cloud_run_revision" --limit=50

# Check pipeline metrics
python monitoring/pipeline-monitoring.py

# Monitor VM operations
gcloud compute operations list --filter="operationType:insert"
```

## Security Considerations

1. **SSH Keys**: Stored securely in environment variables
2. **Service Authentication**: Uses service account with minimal required permissions
3. **Network Security**: VMs created in private subnets with controlled access
4. **Data Protection**: Database dumps handled securely with automatic cleanup

## Next Steps

1. **Scheduled Execution**: Configure Cloud Scheduler for automated pipeline runs
2. **Multi-Environment**: Deploy separate services for dev/staging/prod
3. **Advanced Monitoring**: Set up custom dashboards and alerting rules
4. **Performance Optimization**: Fine-tune resource allocation and timeout settings
