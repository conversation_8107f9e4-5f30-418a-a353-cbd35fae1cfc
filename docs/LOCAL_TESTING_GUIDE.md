# Local Testing Guide for Database Pipeline

This guide explains how to test the complete database pipeline locally without running the GitHub Actions workflow. This allows you to develop, test, and debug the pipeline on your local machine before pushing to GitHub.

## Overview

The database pipeline performs these steps:
1. **Database Dump**: SSH to GCP VM and dump MySQL 'forge' database (excluding activity_log table)
2. **Database Import**: Import the dump into a local MySQL 'forge' database
3. **Database Validation**: Execute query `SELECT COUNT(*) FROM trips WHERE is_booker_version=1`

## Prerequisites

### Required Tools
- **Terraform** (v1.6.0+): For infrastructure management
- **Google Cloud SDK**: For GCP authentication and VM management
- **MySQL/MariaDB**: For local database operations
- **SSH**: For connecting to the GCP VM
- **Python 3.8+**: For running the pipeline scripts

### Required Environment Variables

Set these environment variables before running local tests:

```bash
# GCP Configuration
export TF_VAR_project_id="your-gcp-project-id"

# AWS EC2 Configuration (your remote database server)
export TF_VAR_aws_hostname="***********"  # Your AWS EC2 IP
export TF_VAR_aws_user="forge"  # AWS EC2 username
export TF_VAR_aws_private_key="-----BEGIN OPENSSH PRIVATE KEY-----
your-private-key-content-here
-----END OPENSSH PRIVATE KEY-----"
export TF_VAR_aws_public_key="ssh-rsa your-public-key-content-here user@hostname"

# GitHub Configuration
export TF_VAR_github_token="ghp_your_github_personal_access_token"
```

### GCP Authentication

Authenticate with Google Cloud:

```bash
gcloud auth login
gcloud config set project your-gcp-project-id
```

## Local Testing Methods

### Method 1: Automated Complete Test (Recommended)

Use the automated test setup script that handles everything:

```bash
# Run complete test cycle (deploy VM, run pipeline, cleanup)
./local_test_setup.py full

# Or run individual steps:
./local_test_setup.py check      # Check prerequisites only
./local_test_setup.py deploy     # Deploy infrastructure only
./local_test_setup.py test       # Run pipeline test only
./local_test_setup.py cleanup    # Cleanup infrastructure only
```

### Method 2: Manual Step-by-Step Testing

#### Step 1: Deploy GCP Infrastructure

```bash
cd infrastructure/terraform

# Initialize Terraform
terraform init

# Create terraform.tfvars (or use the setup script)
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Plan and apply
terraform plan
terraform apply
```

#### Step 2: Wait for VM Setup

Wait 5-10 minutes for the VM to boot and run the startup script. You can monitor progress:

```bash
# Check VM status
gcloud compute instances list --filter="name:data-pipeline-dev-pipeline-vm"

# View startup script logs
gcloud compute instances get-serial-port-output data-pipeline-dev-pipeline-vm --zone=europe-west10-c
```

#### Step 3: Run the Database Pipeline

Choose one of these methods:

**Python Pipeline (Recommended):**
```bash
python run_pipeline.py
```

**Shell Pipeline (Alternative):**
```bash
./run_database_pipeline.sh
```

#### Step 4: Verify Results

The pipeline should output:
```
DB Query output resulting in these rows: [count]
```

Check the dump file was created:
```bash
ls -la ~/Documents/cuba_buddy/forge_dump.sql
```

#### Step 5: Cleanup Infrastructure

```bash
cd infrastructure/terraform
terraform destroy
```

## Expected Output

### Successful Pipeline Run

```
[2024-01-XX XX:XX:XX] === Starting Enhanced Database Pipeline ===
[2024-01-XX XX:XX:XX] Pipeline Steps:
[2024-01-XX XX:XX:XX] 1. Test SSH connection to GCP VM
[2024-01-XX XX:XX:XX] 2. Dump 'forge' database (excluding activity_log table)
[2024-01-XX XX:XX:XX] 3. Import dump to local MySQL 'forge' database
[2024-01-XX XX:XX:XX] 4. Execute validation query on trips table

[2024-01-XX XX:XX:XX] --- Step 1: Testing SSH Connection ---
[2024-01-XX XX:XX:XX] Testing SSH connection to AWS EC2 using 'trips' alias...
[2024-01-XX XX:XX:XX] SSH connection successful
[2024-01-XX XX:XX:XX] MySQL available on remote host: mysql Ver 15.1 Distrib 10.x.x-MariaDB

[2024-01-XX XX:XX:XX] --- Step 2: Dumping Database from GCP VM ---
[2024-01-XX XX:XX:XX] Starting MySQL database dump from GCP VM...
[2024-01-XX XX:XX:XX] Database dump completed successfully!
[2024-01-XX XX:XX:XX] Dump file size: XXXXX bytes
[2024-01-XX XX:XX:XX] Dump file location: /Users/<USER>/Documents/cuba_buddy/forge_dump.sql

[2024-01-XX XX:XX:XX] --- Step 3: Importing Database to Local MySQL ---
[2024-01-XX XX:XX:XX] Database 'forge' is ready
[2024-01-XX XX:XX:XX] Data imported successfully into 'forge' database
[2024-01-XX XX:XX:XX] ✓ 'trips' table found in the database

[2024-01-XX XX:XX:XX] --- Step 4: Executing Validation Query ---
[2024-01-XX XX:XX:XX] DB Query output resulting in these rows: 42
DB Query output resulting in these rows: 42

[2024-01-XX XX:XX:XX] === Database Pipeline Completed Successfully ===
[2024-01-XX XX:XX:XX] Total execution time: 0:05:23.123456
[2024-01-XX XX:XX:XX] Final validation result: 42 rows found
```

## Troubleshooting

### Common Issues

#### 1. SSH Connection Failed
```bash
# Check SSH configuration
cat ~/.ssh/config

# Test SSH connection manually
ssh trips 'echo "test"'

# Check SSH agent
ssh-add -l
```

#### 2. MySQL Connection Issues
```bash
# Check MySQL service
sudo systemctl status mysql

# Test MySQL connection
mysql -u pipeline -ppipeline123 -e "SHOW DATABASES;"
```

#### 3. GCP VM Not Ready
```bash
# Check VM status
gcloud compute instances describe cuba-buddy-data-pipeline-dev --zone=europe-west3-a

# View startup logs
gcloud compute instances get-serial-port-output cuba-buddy-data-pipeline-dev --zone=europe-west3-a
```

#### 4. Terraform Issues
```bash
# Check Terraform state
terraform show

# Force refresh state
terraform refresh

# Reset if needed
terraform destroy
rm -rf .terraform terraform.tfstate*
terraform init
```

### Debug Mode

Enable debug logging:

```bash
# For Python pipeline
export PYTHONPATH=.
python -c "import logging; logging.basicConfig(level=logging.DEBUG)" run_pipeline.py

# For shell pipeline
bash -x run_database_pipeline.sh
```

## File Locations

- **Pipeline Scripts**: `run_pipeline.py`, `run_database_pipeline.sh`
- **Local Test Setup**: `local_test_setup.py`
- **Terraform Config**: `infrastructure/terraform/`
- **Dump File**: `~/Documents/cuba_buddy/forge_dump.sql`
- **Logs**: `pipeline_execution.log`, `local_test_setup.log`

## Development Workflow

1. **Develop**: Make changes to pipeline scripts
2. **Test Locally**: Use `./local_test_setup.py full` to test complete cycle
3. **Debug**: Use individual commands if issues arise
4. **Iterate**: Repeat until pipeline works correctly
5. **Push**: Commit and push to GitHub
6. **GitHub Test**: Run GitHub Actions workflow to test in CI/CD environment

## Cost Management

- **VM Costs**: ~$0.10-0.20 per hour for e2-medium instance
- **Auto-cleanup**: The `auto_delete_vm = true` setting ensures VMs are destroyed automatically
- **Manual Cleanup**: Always run `terraform destroy` if tests are interrupted

## Next Steps

After successful local testing:

1. Commit your changes to Git
2. Push to GitHub repository
3. Run the GitHub Actions workflow manually to test in the CI/CD environment
4. Monitor the workflow execution and logs
5. Make any necessary adjustments based on CI/CD results

This local testing approach allows you to iterate quickly and catch issues early before they reach the CI/CD pipeline.
