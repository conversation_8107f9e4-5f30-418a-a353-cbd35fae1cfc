#!/bin/bash

# Deploy Cloud Run Services Script
# This script builds and deploys all Cloud Run services for the data pipeline

set -e

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}
REGION=${GCP_REGION:-"europe-west10"}
ENVIRONMENT=${ENVIRONMENT:-"dev"}

if [ -z "$PROJECT_ID" ]; then
    echo "Error: GCP_PROJECT_ID not set and no default project configured"
    exit 1
fi

echo "=== Deploying Cloud Run Services ==="
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Environment: $ENVIRONMENT"

# Create Artifact Registry repository if it doesn't exist
REPO_NAME="data-pipeline-${ENVIRONMENT}-repo"
echo "Creating Artifact Registry repository: $REPO_NAME"

gcloud artifacts repositories create $REPO_NAME \
    --repository-format=docker \
    --location=$REGION \
    --description="Container repository for pipeline services" \
    --project=$PROJECT_ID || echo "Repository may already exist"

# Configure Docker authentication
echo "Configuring Docker authentication..."
gcloud auth configure-docker ${REGION}-docker.pkg.dev

# Build and push orchestrator service
echo "=== Building and deploying Orchestrator service ==="
cd cloud-functions/orchestrator

# Build image
IMAGE_NAME="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/orchestrator:latest"
echo "Building image: $IMAGE_NAME"

docker build -t $IMAGE_NAME .
docker push $IMAGE_NAME

# Deploy to Cloud Run
gcloud run deploy data-pipeline-${ENVIRONMENT}-orchestrator \
    --image=$IMAGE_NAME \
    --platform=managed \
    --region=$REGION \
    --allow-unauthenticated \
    --memory=2Gi \
    --cpu=2 \
    --timeout=3600 \
    --max-instances=10 \
    --set-env-vars="GCP_PROJECT_ID=${PROJECT_ID},GCP_REGION=${REGION},ENVIRONMENT=${ENVIRONMENT}" \
    --service-account="vm-cuba-buddy-data-ingestion@${PROJECT_ID}.iam.gserviceaccount.com" \
    --project=$PROJECT_ID

cd ../..

# Build and push VM manager service
echo "=== Building and deploying VM Manager service ==="
cd cloud-functions/vm-manager

# Copy terraform files to the build context
mkdir -p terraform
cp -r ../../infrastructure/terraform/* terraform/

IMAGE_NAME="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/vm-manager:latest"
echo "Building image: $IMAGE_NAME"

docker build -t $IMAGE_NAME .
docker push $IMAGE_NAME

# Deploy to Cloud Run
gcloud run deploy data-pipeline-${ENVIRONMENT}-vm-manager \
    --image=$IMAGE_NAME \
    --platform=managed \
    --region=$REGION \
    --no-allow-unauthenticated \
    --memory=2Gi \
    --cpu=2 \
    --timeout=1800 \
    --max-instances=5 \
    --service-account="vm-cuba-buddy-data-ingestion@${PROJECT_ID}.iam.gserviceaccount.com" \
    --project=$PROJECT_ID

# Clean up terraform files
rm -rf terraform

cd ../..

# Build and push database pipeline service
echo "=== Building and deploying Database Pipeline service ==="
cd cloud-functions/database-pipeline

IMAGE_NAME="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/database-pipeline:latest"
echo "Building image: $IMAGE_NAME"

docker build -t $IMAGE_NAME .
docker push $IMAGE_NAME

# Deploy to Cloud Run
gcloud run deploy data-pipeline-${ENVIRONMENT}-db-pipeline \
    --image=$IMAGE_NAME \
    --platform=managed \
    --region=$REGION \
    --no-allow-unauthenticated \
    --memory=1Gi \
    --cpu=1 \
    --timeout=1800 \
    --max-instances=5 \
    --service-account="vm-cuba-buddy-data-ingestion@${PROJECT_ID}.iam.gserviceaccount.com" \
    --project=$PROJECT_ID

cd ../..

# Update orchestrator with service URLs
echo "=== Updating orchestrator with service URLs ==="

VM_MANAGER_URL=$(gcloud run services describe data-pipeline-${ENVIRONMENT}-vm-manager --region=$REGION --format="value(status.url)" --project=$PROJECT_ID)
DB_PIPELINE_URL=$(gcloud run services describe data-pipeline-${ENVIRONMENT}-db-pipeline --region=$REGION --format="value(status.url)" --project=$PROJECT_ID)

gcloud run services update data-pipeline-${ENVIRONMENT}-orchestrator \
    --region=$REGION \
    --update-env-vars="VM_MANAGER_URL=${VM_MANAGER_URL},DB_PIPELINE_URL=${DB_PIPELINE_URL}" \
    --project=$PROJECT_ID

# Get orchestrator URL
ORCHESTRATOR_URL=$(gcloud run services describe data-pipeline-${ENVIRONMENT}-orchestrator --region=$REGION --format="value(status.url)" --project=$PROJECT_ID)

echo "=== Deployment Complete ==="
echo "Orchestrator URL: $ORCHESTRATOR_URL"
echo "VM Manager URL: $VM_MANAGER_URL"
echo "Database Pipeline URL: $DB_PIPELINE_URL"
echo ""
echo "To trigger the pipeline:"
echo "curl -X POST $ORCHESTRATOR_URL/execute-pipeline -H 'Content-Type: application/json' -d '{\"pipeline_id\":\"manual-$(date +%Y%m%d-%H%M%S)\"}'"
echo ""
echo "Health check:"
echo "curl $ORCHESTRATOR_URL/health"
