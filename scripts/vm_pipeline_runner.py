#!/usr/bin/env python3
"""
VM Pipeline Runner
Optimized script that runs on the GCP VM to handle database operations
This script is designed to be executed directly on the VM by Cloud Run services
"""

import os
import sys
import logging
import subprocess
import tempfile
import json
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/vm_pipeline.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class VMPipelineRunner:
    def __init__(self):
        self.start_time = datetime.now()
        self.pipeline_user = "pipeline"
        self.ssh_alias = "trips"
        self.results_dir = Path("/home/<USER>/results")
        self.results_dir.mkdir(exist_ok=True)
        
        logger.info(f"VM Pipeline Runner initialized at {self.start_time}")

    def load_ssh_environment(self):
        """Load SSH agent environment"""
        try:
            ssh_env_file = Path(f"/home/<USER>/.ssh/ssh_agent_env")
            if ssh_env_file.exists():
                with open(ssh_env_file, 'r') as f:
                    for line in f:
                        if line.startswith('export '):
                            key, value = line.replace('export ', '').strip().split('=', 1)
                            os.environ[key] = value
                logger.info("SSH agent environment loaded")
            else:
                logger.warning("SSH agent environment file not found")
                
            # Ensure SSH agent is running and key is loaded
            result = subprocess.run(['ssh-add', '-l'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.info("Starting SSH agent and adding key...")
                subprocess.run(['eval', '$(ssh-agent -s)'], shell=True)
                subprocess.run(['ssh-add', f'/home/<USER>/.ssh/aws_private_key'])
                
        except Exception as e:
            logger.error(f"Error loading SSH environment: {e}")

    def test_ssh_connection(self) -> dict:
        """Test SSH connection to AWS EC2"""
        logger.info("Testing SSH connection to AWS EC2...")
        
        try:
            self.load_ssh_environment()
            
            # Test basic SSH connection
            result = subprocess.run([
                'ssh', '-o', 'ConnectTimeout=30', '-o', 'BatchMode=yes',
                self.ssh_alias, 'echo "SSH connection successful"; hostname; date'
            ], capture_output=True, text=True, timeout=45)
            
            if result.returncode == 0:
                logger.info("SSH connection test passed")
                
                # Test MySQL availability
                mysql_result = subprocess.run([
                    'ssh', self.ssh_alias, 'mysql --version'
                ], capture_output=True, text=True, timeout=30)
                
                mysql_available = mysql_result.returncode == 0
                
                return {
                    'success': True,
                    'ssh_output': result.stdout,
                    'mysql_available': mysql_available,
                    'mysql_version': mysql_result.stdout.strip() if mysql_available else None
                }
            else:
                logger.error(f"SSH connection failed: {result.stderr}")
                return {
                    'success': False,
                    'error': result.stderr,
                    'ssh_output': result.stdout
                }
                
        except subprocess.TimeoutExpired:
            logger.error("SSH connection timeout")
            return {'success': False, 'error': 'SSH connection timeout'}
        except Exception as e:
            logger.error(f"SSH test error: {e}")
            return {'success': False, 'error': str(e)}

    def dump_database_schema(self) -> dict:
        """Dump database schema only"""
        logger.info("Dumping database schema...")
        
        try:
            self.load_ssh_environment()
            
            schema_file = self.results_dir / "forge_schema.sql"
            
            # Dump schema only (no data)
            with open(schema_file, 'w') as f:
                result = subprocess.run([
                    'ssh', self.ssh_alias,
                    'mysqldump --no-data forge --ignore-table=forge.activity_log'
                ], stdout=f, stderr=subprocess.PIPE, text=True, timeout=600)
            
            if result.returncode == 0:
                schema_size = schema_file.stat().st_size
                logger.info(f"Schema dump completed: {schema_size} bytes")
                
                return {
                    'success': True,
                    'file_path': str(schema_file),
                    'file_size': schema_size,
                    'lines': self._count_lines(schema_file)
                }
            else:
                logger.error(f"Schema dump failed: {result.stderr}")
                return {
                    'success': False,
                    'error': result.stderr
                }
                
        except subprocess.TimeoutExpired:
            logger.error("Schema dump timeout")
            return {'success': False, 'error': 'Schema dump timeout'}
        except Exception as e:
            logger.error(f"Schema dump error: {e}")
            return {'success': False, 'error': str(e)}

    def dump_complete_database(self) -> dict:
        """Dump complete database with data"""
        logger.info("Dumping complete database...")
        
        try:
            self.load_ssh_environment()
            
            dump_file = self.results_dir / "forge_complete.sql"
            
            # Dump complete database
            with open(dump_file, 'w') as f:
                result = subprocess.run([
                    'ssh', self.ssh_alias,
                    'mysqldump --single-transaction forge --ignore-table=forge.activity_log'
                ], stdout=f, stderr=subprocess.PIPE, text=True, timeout=1800)
            
            if result.returncode == 0:
                dump_size = dump_file.stat().st_size
                logger.info(f"Complete database dump completed: {dump_size} bytes")
                
                # Show sample of the dump
                sample_lines = self._get_file_sample(dump_file, 20)
                
                return {
                    'success': True,
                    'file_path': str(dump_file),
                    'file_size': dump_size,
                    'lines': self._count_lines(dump_file),
                    'sample': sample_lines
                }
            else:
                logger.error(f"Complete database dump failed: {result.stderr}")
                return {
                    'success': False,
                    'error': result.stderr
                }
                
        except subprocess.TimeoutExpired:
            logger.error("Complete database dump timeout")
            return {'success': False, 'error': 'Complete database dump timeout'}
        except Exception as e:
            logger.error(f"Complete database dump error: {e}")
            return {'success': False, 'error': str(e)}

    def execute_validation_query(self) -> dict:
        """Execute the validation query"""
        logger.info("Executing validation query...")
        
        try:
            self.load_ssh_environment()
            
            query = "SELECT COUNT(*) FROM trips WHERE is_booker_version=1"
            
            result = subprocess.run([
                'ssh', self.ssh_alias,
                f'mysql -e "{query}" forge'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                # Parse the result
                output_lines = result.stdout.strip().split('\n')
                if len(output_lines) >= 2:
                    count = output_lines[1].strip()
                    logger.info(f"Validation query result: {count}")
                    
                    # Save result to file
                    result_file = self.results_dir / "query_result.txt"
                    with open(result_file, 'w') as f:
                        f.write(f"{count}\n")
                    
                    return {
                        'success': True,
                        'result': count,
                        'query': query,
                        'raw_output': result.stdout
                    }
                else:
                    logger.error("Unexpected query output format")
                    return {
                        'success': False,
                        'error': 'Unexpected query output format',
                        'raw_output': result.stdout
                    }
            else:
                logger.error(f"Query execution failed: {result.stderr}")
                return {
                    'success': False,
                    'error': result.stderr,
                    'raw_output': result.stdout
                }
                
        except subprocess.TimeoutExpired:
            logger.error("Query execution timeout")
            return {'success': False, 'error': 'Query execution timeout'}
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            return {'success': False, 'error': str(e)}

    def run_complete_pipeline(self) -> dict:
        """Run the complete pipeline"""
        logger.info("=== Starting Complete VM Pipeline ===")
        
        pipeline_result = {
            'start_time': self.start_time.isoformat(),
            'steps': {},
            'status': 'running'
        }
        
        try:
            # Step 1: Test SSH connection
            logger.info("Step 1: Testing SSH connection")
            ssh_result = self.test_ssh_connection()
            pipeline_result['steps']['ssh_test'] = ssh_result
            
            if not ssh_result.get('success'):
                raise Exception(f"SSH test failed: {ssh_result.get('error')}")
            
            # Step 2: Dump schema
            logger.info("Step 2: Dumping database schema")
            schema_result = self.dump_database_schema()
            pipeline_result['steps']['schema_dump'] = schema_result
            
            if not schema_result.get('success'):
                logger.warning(f"Schema dump failed: {schema_result.get('error')}")
            
            # Step 3: Dump complete database
            logger.info("Step 3: Dumping complete database")
            dump_result = self.dump_complete_database()
            pipeline_result['steps']['complete_dump'] = dump_result
            
            if not dump_result.get('success'):
                raise Exception(f"Database dump failed: {dump_result.get('error')}")
            
            # Step 4: Execute validation query
            logger.info("Step 4: Executing validation query")
            query_result = self.execute_validation_query()
            pipeline_result['steps']['validation_query'] = query_result
            
            if not query_result.get('success'):
                raise Exception(f"Validation query failed: {query_result.get('error')}")
            
            # Success
            end_time = datetime.now()
            duration = end_time - self.start_time
            
            pipeline_result.update({
                'status': 'completed',
                'end_time': end_time.isoformat(),
                'duration': str(duration),
                'final_result': query_result.get('result')
            })
            
            logger.info(f"=== Pipeline Completed Successfully ===")
            logger.info(f"Duration: {duration}")
            logger.info(f"Final result: {query_result.get('result')}")
            
            return pipeline_result
            
        except Exception as e:
            end_time = datetime.now()
            duration = end_time - self.start_time
            
            pipeline_result.update({
                'status': 'failed',
                'end_time': end_time.isoformat(),
                'duration': str(duration),
                'error': str(e)
            })
            
            logger.error(f"=== Pipeline Failed ===")
            logger.error(f"Error: {e}")
            logger.error(f"Duration: {duration}")
            
            return pipeline_result

    def _count_lines(self, file_path: Path) -> int:
        """Count lines in a file"""
        try:
            with open(file_path, 'r') as f:
                return sum(1 for _ in f)
        except:
            return 0

    def _get_file_sample(self, file_path: Path, num_lines: int = 10) -> list:
        """Get sample lines from a file"""
        try:
            with open(file_path, 'r') as f:
                return [f.readline().strip() for _ in range(num_lines)]
        except:
            return []

def main():
    """Main entry point"""
    if len(sys.argv) < 2:
        print("Usage: python vm_pipeline_runner.py <command>")
        print("Commands: test-ssh, dump-schema, dump-complete, execute-query, run-all")
        sys.exit(1)
    
    command = sys.argv[1]
    runner = VMPipelineRunner()
    
    try:
        if command == "test-ssh":
            result = runner.test_ssh_connection()
        elif command == "dump-schema":
            result = runner.dump_database_schema()
        elif command == "dump-complete":
            result = runner.dump_complete_database()
        elif command == "execute-query":
            result = runner.execute_validation_query()
        elif command == "run-all":
            result = runner.run_complete_pipeline()
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
        
        # Output result as JSON
        print(json.dumps(result, indent=2))
        
        # Exit with appropriate code
        sys.exit(0 if result.get('success', False) or result.get('status') == 'completed' else 1)
        
    except Exception as e:
        logger.error(f"Pipeline runner error: {e}")
        print(json.dumps({'success': False, 'error': str(e)}, indent=2))
        sys.exit(1)

if __name__ == "__main__":
    main()
