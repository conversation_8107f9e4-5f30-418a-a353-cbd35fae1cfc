#!/bin/bash

# Cleanup Failed Cloud Run Deployment Script
# This script cleans up any failed Cloud Run deployments

set -e

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}
REGION=${GCP_REGION:-"europe-west3"}
ENVIRONMENT=${ENVIRONMENT:-"dev"}

if [ -z "$PROJECT_ID" ]; then
    echo "Error: GCP_PROJECT_ID not set and no default project configured"
    exit 1
fi

echo "=== Cleaning up failed Cloud Run deployment ==="
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Environment: $ENVIRONMENT"

# Delete any existing Cloud Run services
echo "Deleting existing Cloud Run services..."

gcloud run services delete data-pipeline-${ENVIRONMENT}-orchestrator \
    --region=$REGION \
    --project=$PROJECT_ID \
    --quiet || echo "Orchestrator service not found or already deleted"

gcloud run services delete data-pipeline-${ENVIRONMENT}-vm-manager \
    --region=$REGION \
    --project=$PROJECT_ID \
    --quiet || echo "VM Manager service not found or already deleted"

gcloud run services delete data-pipeline-${ENVIRONMENT}-db-pipeline \
    --region=$REGION \
    --project=$PROJECT_ID \
    --quiet || echo "Database Pipeline service not found or already deleted"

# Delete the Artifact Registry repository
echo "Deleting Artifact Registry repository..."
REPO_NAME="data-pipeline-${ENVIRONMENT}-repo"

gcloud artifacts repositories delete $REPO_NAME \
    --location=$REGION \
    --project=$PROJECT_ID \
    --quiet || echo "Repository not found or already deleted"

echo "=== Cleanup completed ==="
echo "You can now run the deployment script again:"
echo "./scripts/deploy-cloud-run-services.sh"
